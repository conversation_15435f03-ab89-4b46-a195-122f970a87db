package com.zsmall.order.biz.factory;

import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.tiktok.OperationEnums;
import com.zsmall.common.handler.AbstractOrderBaseHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/30 18:14
 */
@Component
public class ThirdOrderSaveApiFactory {
    @Resource
    private Map<String, AbstractOrderBaseHandler> strategyMap;


    /**
     * 功能描述：获取调用策略
     *
     * @param channelType 通道类型
     * @param enums       枚举
     * @return {@link AbstractOrderBaseHandler }
     * <AUTHOR>
     * @date 2024/01/30
     */
    public AbstractOrderBaseHandler getInvokeStrategy(ChannelTypeEnum channelType, OperationEnums enums){
        return strategyMap.get(channelType+":"+enums.name());
    }
}
