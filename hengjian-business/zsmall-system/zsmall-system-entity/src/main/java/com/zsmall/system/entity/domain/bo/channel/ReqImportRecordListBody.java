package com.zsmall.system.entity.domain.bo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求信息-订单导入审核
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "请求信息-查询导入记录")
public class ReqImportRecordListBody {

    @Schema(title = "订单编号")
    private String orderNo;

    @Schema(title = "排序方式：Desc-按照时间倒序，Asc-按照时间正序")
    private String sort;

}
