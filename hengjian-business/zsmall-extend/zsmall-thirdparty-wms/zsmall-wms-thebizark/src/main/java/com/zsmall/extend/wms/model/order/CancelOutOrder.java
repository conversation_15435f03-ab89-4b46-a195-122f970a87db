package com.zsmall.extend.wms.model.order;

import lombok.Data;

import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/20 15:40
 */
@Data
public class CancelOutOrder {
    private long createdAt;
    private long createdBy;
    private String createdName;
    /**
     * 发货单拦截状态，0拦截中1拦截成功2拦截失败-1接口调用失败或异常3部分拦截
     */
    private Integer deliverInterceptStatus;
    private long deliverInterceptUpdateTime;
    private long deliverOrderId;
    /**
     * 发货单号
     */
    private String deliverOrderNo;
    private long disabledBy;
    private long id;
    private long interceptId;
    /**
     * 拦截单号
     */
    private String interceptNo;
    private long orderCancelId;
    private long orderId;
    private String orderNo;
    private long organizationId;
    private Map<String, Object> params;
    private String reverseOrderId;
    private long shopId;
    private long updatedBy;
}
