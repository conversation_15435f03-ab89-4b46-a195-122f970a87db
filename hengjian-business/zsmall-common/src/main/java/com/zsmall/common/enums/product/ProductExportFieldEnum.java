package com.zsmall.common.enums.product;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * 商品自定义导出字段枚举
 */
@Getter
@AllArgsConstructor
public enum ProductExportFieldEnum {

    productName("商品名称", "Product Name"),
    productCategory("商品分类", "Product Category"),
    productLabel("商品标签", "Product Label"),
    createTime("创建时间", "Create Time"),
    sku("Sku", "Sku"),
    erpSku("Erp Sku", "Erp Sku"),
    productSkuCode("Item No.", "Item No."),
    skuSpec("商品规格", "Product Spec"),
    pickUpPrice("自提价", "PickUp Price"),
    dropShippingPrice("代发价", "DropShipping Price"),
    msrp("建议零售价", "MSRP"),
    stockQuantity("库存数量", "Stock Quantity"),
    supplierId("供应商ID", "Supplier ID"),
    stockManager("库存管理方", "Stock Manager"),
    reviewState("SPU审核状态", "SPU Review State"),
    shelfState("SPU上/下架", "SPU On/Off Shelf"),
    forbiddenChannel("禁售渠道", "Drop-Shipping Prohibition"),
    supportedLogistics("支持的物流", "Supported Logistics"),
    length("长度", "Length"),
    width("宽度", "Width"),
    height("高度", "Height"),
    lengthUnit("尺寸单位", "Dimensional Units"),
    weight("重量", "Weight"),
    weightUnit("重量单位", "Weight Unit"),
    packLength("打包长度", "Pack Length"),
    packWidth("打包宽度", "Pack Width"),
    packHeight("打包高度", "Pack Height"),
    packLengthUnit("打包尺寸单位", "Pack Dimensional Unit"),
    packWeight("打包重量", "Pack Weight"),
    packWeightUnit("打包重量单位", "Pack Weight Unit"),
    skuShowUrl("图片URL", "Images"),
    skuShelfState("SKU销售状态", "SKU Sales Status"),
    supplierEmail("供应商邮箱", "Supplier Email"),
    warehouseName("仓库名称","Warehouse Name"),
    warehouseCode("仓库编码", "Warehouse Code"),
    warehouseSystemCode("仓库系统编号", "Warehouse System Code"),
    phoneNumber("供应商手机号", "Phone Number"),
    productLink("商品链接", "Product Link"),
    productShowUrl("首图URL", "First Image Url"),
    unitPrice("产品单价", "Unit Price"),
    operateFee("操作费", "Operate Fee"),
    finalDeliveryFee("尾程派送费", "Final Delivery Fee");

    private final String zh_CN;
    private final String en_US;

    public String getFieldName(String language) {
        if (Locale.SIMPLIFIED_CHINESE.toString().equals(language)) {
            return zh_CN;
        } else {
            return en_US;
        }
    }

    public static List<ProductExportFieldEnum> valueOfByList(List<String> valueList) {
        return valueList.stream().map(value -> ProductExportFieldEnum.valueOf(value)).collect(Collectors.toList());
    }

}
