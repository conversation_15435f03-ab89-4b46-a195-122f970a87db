package com.zsmall.order.biz.factory.impl;

import cn.hutool.core.util.NumberUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.constant.OrderRefundRuleConstant;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.order.factory.RefundRuleFactory;
import com.zsmall.order.factory.RefundRuleService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.OrderRefundItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 是否支持修改金额
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@Slf4j
@Service("whetherSupportModifyAmount")
public class WhetherSupportModifyAmount implements RefundRuleService {

    @Override
    public void afterPropertiesSet() throws Exception {
        RefundRuleFactory.register("WhetherSupportModifyAmount", this);
    }

    /**
     * 售后规则“是否”类条件处理
     *
     * @param whetherValue
     * @param orderItem
     * @param orderRefund
     * @param orderRefundItem
     */
    @Override
    public void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo bo,
                                        OrderItem orderItem, OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException {

        BigDecimal refundExecutableAmount = orderItem.getPlatformRefundExecutableAmount();
        BigDecimal refundExecutableAmountSup = orderItem.getOriginalRefundExecutableAmount();

        orderRefundItem.setPlatformPayableTotalAmount(orderItem.getPlatformPayableTotalAmount());
        orderRefundItem.setOriginalPayableTotalAmount(orderItem.getOriginalPayableTotalAmount());
        orderRefundItem.setPlatformPrepaidTotalAmount(orderItem.getPlatformPrepaidTotalAmount());
        orderRefundItem.setOriginalPrepaidTotalAmount(orderItem.getOriginalPrepaidTotalAmount());
        orderRefundItem.setPlatformActualTotalAmount(orderItem.getPlatformActualTotalAmount());
        orderRefundItem.setOriginalActualTotalAmount(orderItem.getOriginalActualTotalAmount());

        if (OrderRefundRuleConstant.WhetherSupportModifyAmount.UNSUPPORTED.equals(whetherValue)) {
            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
            orderRefund.setPlatformRefundAmount(refundExecutableAmount);
            orderRefund.setOriginalRefundAmount(refundExecutableAmountSup);
            orderItem.setOrderState(OrderStateType.Verifying);
        } else if (OrderRefundRuleConstant.WhetherSupportModifyAmount.SUPPORT.equals(whetherValue)) {
            BigDecimal applyAmount = bo.getAmount();

            if (NumberUtil.isLessOrEqual(applyAmount, BigDecimal.ZERO)) {
                String activityType = orderRefundItem.getActivityType();
                if (ProductActivityTypeEnum.Buyout.name().equals(activityType)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.UNMODIFIABLE_AMOUNT_REASON);
                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.AMOUNT_CANNOT_BE_ZERO);
                }
            }

            if (NumberUtil.isGreater(applyAmount, refundExecutableAmount)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUESTED_AMOUNT_EXCEEDS_REFUNDABLE);
            }

            BigDecimal refundPriceSup = calculateRefundPriceSup(applyAmount, refundExecutableAmount, refundExecutableAmountSup);

            orderRefund.setPlatformRefundAmount(applyAmount);
            orderRefund.setOriginalRefundAmount(refundPriceSup);

            if (NumberUtil.equals(applyAmount, refundExecutableAmount)) {
                orderItem.setOrderState(OrderStateType.Verifying);
            }

            BigDecimal newRefundExecutableAmount = NumberUtil.sub(refundExecutableAmount, applyAmount);
            BigDecimal newRefundExecutableAmountSup = NumberUtil.sub(refundExecutableAmountSup, refundPriceSup);
            orderItem.setPlatformRefundExecutableAmount(newRefundExecutableAmount);
            orderItem.setOriginalRefundExecutableAmount(newRefundExecutableAmountSup);
        }

    }

    /**
     * 售后规则“是否”类条件处理（主订单适用）
     *
     * @param whetherValue
     * @param bo
     * @param order
     * @param orderItem
     * @param orderRefund
     * @param orderRefundItem
     */
    @Override
    public void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo bo, Orders order,
                                        OrderItem orderItem, OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException {

        BigDecimal refundExecutableAmount = order.getPlatformRefundExecutableAmount();
        BigDecimal refundExecutableAmountSup = order.getOriginalRefundExecutableAmount();

        // 退款金额（平台）不为空，说明已经设置过，不需要重复计算
        BigDecimal refundPrice = orderRefund.getPlatformRefundAmount();
        if (refundPrice != null) {
            return;
        }

        orderRefundItem.setPlatformPayableTotalAmount(orderItem.getPlatformPayableTotalAmount());
        orderRefundItem.setOriginalPayableTotalAmount(orderItem.getOriginalPayableTotalAmount());
        orderRefundItem.setPlatformPrepaidTotalAmount(orderItem.getPlatformPrepaidTotalAmount());
        orderRefundItem.setOriginalPrepaidTotalAmount(orderItem.getOriginalPrepaidTotalAmount());
        orderRefundItem.setPlatformActualTotalAmount(orderItem.getPlatformActualTotalAmount());
        orderRefundItem.setOriginalActualTotalAmount(orderItem.getOriginalActualTotalAmount());

        if (OrderRefundRuleConstant.WhetherSupportModifyAmount.UNSUPPORTED.equals(whetherValue)) {
            order.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            order.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
            orderRefund.setPlatformRefundAmount(refundExecutableAmount);
            orderRefund.setOriginalRefundAmount(refundExecutableAmountSup);

            order.setOrderState(OrderStateType.Verifying);
            orderItem.setOrderState(OrderStateType.Verifying);
        } else if (OrderRefundRuleConstant.WhetherSupportModifyAmount.SUPPORT.equals(whetherValue)) {
            BigDecimal applyAmount = bo.getAmount();
            if (NumberUtil.isLessOrEqual(applyAmount, BigDecimal.ZERO)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.AMOUNT_CANNOT_BE_ZERO);
            }

            if (NumberUtil.isGreater(applyAmount, refundExecutableAmount)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUESTED_AMOUNT_EXCEEDS_REFUNDABLE);
            }

            BigDecimal refundPriceSup = calculateRefundPriceSup(applyAmount, refundExecutableAmount, refundExecutableAmountSup);
            orderRefund.setPlatformRefundAmount(applyAmount);
            orderRefund.setOriginalRefundAmount(refundPriceSup);

            if (NumberUtil.equals(applyAmount, refundExecutableAmount)) {
                order.setOrderState(OrderStateType.Verifying);
                orderItem.setOrderState(OrderStateType.Verifying);
            }

            BigDecimal newRefundExecutableAmount = NumberUtil.sub(refundExecutableAmount, applyAmount);
            BigDecimal newRefundExecutableAmountSup = NumberUtil.sub(refundExecutableAmountSup, refundPriceSup);
            order.setPlatformRefundExecutableAmount(newRefundExecutableAmount);
            order.setOriginalRefundExecutableAmount(newRefundExecutableAmountSup);
        }

    }

    /**
     * 售后申请金额转换供货商金额
     *
     * @param applyAmount
     * @param refundExecutableAmount
     * @param refundExecutableAmountSup
     * @return
     */
    private BigDecimal calculateRefundPriceSup(BigDecimal applyAmount, BigDecimal refundExecutableAmount, BigDecimal refundExecutableAmountSup) {
        // （分销商申请金额 / 分销商可执行总金额）x 供应商成本总金额
        BigDecimal result = NumberUtil.div(applyAmount, refundExecutableAmount).multiply(refundExecutableAmountSup).setScale(2, RoundingMode.HALF_UP);
        log.info("售后申请金额转换供货商金额 (申请金额 {} / 分销商可执行总金额 {}) x 供应商成本总金额 {} = 结果 {}", applyAmount, refundExecutableAmount, refundExecutableAmountSup, result);
        return result;
    }

}
