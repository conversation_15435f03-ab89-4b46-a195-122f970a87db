package com.zsmall.extend.shop.rakuten.model.genres;

import lombok.Data;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * 响应体-乐天市场品类
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@Data
public class OutGenresGet implements Serializable {

    private static final long serialVersionUID = 1L;

    private Version version;
    private OutGenre genre;

    @Data
    public class Version {
        private Integer id;
        private ZonedDateTime fixedAt;
    }

}
