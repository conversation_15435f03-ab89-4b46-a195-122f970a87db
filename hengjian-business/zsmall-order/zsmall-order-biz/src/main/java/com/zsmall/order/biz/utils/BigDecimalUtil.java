package com.zsmall.order.biz.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2024年4月26日  17:58
 * @description:
 */
public class BigDecimalUtil {

    /**
     * 确保BigDecimal对象保留两位小数。
     * 如果数值已经是两位小数，则直接返回。
     * 如果小数部分不足两位，则补零。
     *
     * @param value 需要处理的BigDecimal数值
     * @return 保留两位小数的BigDecimal对象
     */
    public static BigDecimal retainTwoDecimals(BigDecimal value) {
        if (value == null) {
            return null;
        }
        // 如果已经小于1，则不需要调用setScale，直接返回
        if (value.compareTo(BigDecimal.ONE) < 0) {
            return value;
        }
        // 保留两位小数，使用HALF_UP模式进行四舍五入
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 将一个double值转换为保留两位小数的BigDecimal对象。
     * @param value double值
     * @return 保留两位小数的BigDecimal对象
     */
    public static BigDecimal fromDouble(double value) {
        return retainTwoDecimals(BigDecimal.valueOf(value));
    }

    /**
     * 将一个int值转换为保留两位小数的BigDecimal对象。
     * @param value int值
     * @return 保留两位小数的BigDecimal对象
     */
    public static BigDecimal fromInt(int value) {
        return retainTwoDecimals(BigDecimal.valueOf(value));
    }

    /**
     * 将一个int值转换为保留两位小数的BigDecimal对象。
     * @param value int值
     * @return 保留两位小数的BigDecimal对象
     */
    public static BigDecimal fromString(String value) {
        return retainTwoDecimals(new BigDecimal(value));
    }
}
