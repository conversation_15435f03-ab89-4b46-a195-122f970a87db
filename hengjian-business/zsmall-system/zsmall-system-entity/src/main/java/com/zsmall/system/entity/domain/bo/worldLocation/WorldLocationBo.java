package com.zsmall.system.entity.domain.bo.worldLocation;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.WorldLocation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 世界地点业务对象 world_location
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WorldLocation.class, reverseConvertGenerate = false)
public class WorldLocationBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 父级地点主键
     */
    private Long parentId;

    /**
     * 父级地点编号
     */
    private String parentCode;

    /**
     * 地点类型（0-洲，1-国家，2-州/省/地区）
     */
    @NotNull(message = "地点类型（0-洲，1-国家，2-州/省/地区）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer locationType;

    /**
     * 地点区号（仅国家类型的需要填写）
     */
    private String locationAreaCode;

    /**
     * 地点名称
     */
    @NotBlank(message = "地点名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String locationName;

    /**
     * 地点名称（JSON，key-语种，value-对应语种名称）
     */
    private JSONObject locationOtherName;

    /**
     * 地点编号
     */
    private String locationCode;

    /**
     * 地点邮编（目前仅州/省/地区类型的需要填，数据格式为JSON数组）
     */
    private JSONArray locationZipCode;


}
