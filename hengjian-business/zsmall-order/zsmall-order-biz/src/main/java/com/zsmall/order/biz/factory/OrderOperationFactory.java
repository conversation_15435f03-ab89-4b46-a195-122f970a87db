package com.zsmall.order.biz.factory;

import com.zsmall.common.handler.AbstractOrderBusinessHandler;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/7/2 23:39
 */
@Component
public class OrderOperationFactory {
    @Resource
    private Map<String, AbstractOrderBusinessHandler> strategyMap;

    public AbstractOrderBusinessHandler getInvokeStrategy(String strategyName){
        return strategyMap.get(strategyName);
    }
}
