package com.zsmall.extend.wms.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 产品
 */
@Setter
@Getter
@Accessors(chain = true)
public class OutProductItems {

    /**
     * sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 销售金额 , 当前sku总金额
     */
    private Double skuAmount;

    /**
     * 投保金额
     */
    private Double insureAmount;

    /**
     * 是否保价 0否1是
     */
    private Integer isInsure;

    /**
     * 是否签收 0否 1是
     */
    private Integer isSignature;

}
