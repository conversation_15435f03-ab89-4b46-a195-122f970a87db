package com.zsmall.order.entity.domain.bo.order;

import lombok.Data;

import java.util.List;

/**
 * 请求体-订单支付
 *
 * <AUTHOR>
 * @date 2023/6/15
 */
@Data
public class OrderPayBo {

    /**
     * 订单编号数组
     */
    private List<String> orderNoList;

    /**
     * 支付密码
     */
    private String paymentPassword;

    private String tenantId;
    /**
     * 功能描述：添加订单编号列表
     *
     * @param orderNoList 订单号列表
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2024/01/21
     */
    public List<String> addOrderNoList(List<String> orderNoList){
        this.orderNoList = orderNoList;
        return this.orderNoList;

    }

}
