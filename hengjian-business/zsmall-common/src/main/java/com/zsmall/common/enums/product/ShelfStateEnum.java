package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Locale;

/**
 * 货架状态枚举
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
@AllArgsConstructor
@Getter
public enum ShelfStateEnum implements IEnum<String> {

    /**
     * 上架
     */
    OnShelf("上架", "OnShelf"),

    /**
     * 下架
     */
    OffShelf("下架", "OffShelf"),

    /**
     * 强制下架
     */
    ForcedOffShelf("下架", "OffShelf"),

    ;

    private String zh_CN;

    private String en_US;

    public String getByLocale(String language) {
        if (Locale.SIMPLIFIED_CHINESE.toString().equals(language)) {
            return zh_CN;
        } else {
            return en_US;
        }
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
