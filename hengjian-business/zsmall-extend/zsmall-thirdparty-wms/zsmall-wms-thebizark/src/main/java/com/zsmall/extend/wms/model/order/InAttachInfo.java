package com.zsmall.extend.wms.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 附件列表
 */
@Setter
@Getter
@Accessors(chain = true)
public class InAttachInfo {

    /**
     * 附件名称
     */
    private String name;

    /**
     * 附件url
     */
    private String url;

    /**
     * 订单附件类型 0:Shippinglabel ;1:Packinglist ;2:BOL ; 3:Other
     */
    private Integer type;

    /**
     * 备注
     */
    private String remark;

    /**
     * base64 字符串,如果填写，则不认上面url里面的内容( url 与file必填其一 )
     */
    private String file;

}
