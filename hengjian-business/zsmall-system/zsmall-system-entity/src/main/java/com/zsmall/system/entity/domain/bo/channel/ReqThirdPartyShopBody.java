package com.zsmall.system.entity.domain.bo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021-03-05 21:42
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(name = "请求信息-第三方授权登录")
public class ReqThirdPartyShopBody {

    /**
     * dispatchType: 店铺授权类型，AMAZON_AUTHORIZE（目前只有亚马逊授权需要）
     */
    @Schema(title = "店铺授权类型，AMAZON_AUTHORIZE（目前只有亚马逊授权需要）")
    private String dispatchType;

    /**
     * 授权唯一标识：可根据当前字段获取缓存内容
     */
    @Schema(title = "授权唯一标识")
    private String authorizeState;

}
