package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.orderRefund.ReturnLogisticsType;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 售后退货物流对象 order_refund_logistics
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_refund_logistics")
public class OrderRefundLogistics extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 售后主单主键
     */
    private Long orderRefundId;

    /**
     * 售后子单主键
     */
    private Long orderRefundItemId;

    /**
     * 退货物流类型（自选物流寄回、安排物流取回）
     */
    private ReturnLogisticsType logisticsReturnType;

    /**
     * 退货物流承运商
     */
    private String logisticsCarrier;

    /**
     * 退货物流跟踪单号
     */
    private String logisticsTrackingNo;

    /**
     * 退货取件地址
     */
    private String logisticsPickupAddress;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
