package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 重量单位类型
 */
@Getter
@AllArgsConstructor
public enum WeightUnitEnum implements IEnum<String> {

    /**
     * 毫克
     */
    mg(1000000.0, "mg", "毫克"),

    /**
     * 克
     */
    g(1000.0, "g", "克"),

    /**
     * 磅
     */
    lb(2.2046226, "lb", "磅"),

    /**
     * 千克
     */
    kg(1.0, "kg", "千克"),

    /**
     * 吨
     */
    t(0.001, "t", "吨"),

    ;

    private double convert;
    private String unit;
    private String nameCn;

    public static WeightUnitEnum fromValue(double value) {
        for (WeightUnitEnum state : WeightUnitEnum.values()) {
            if (Objects.equals(value, state.getValue())) {
                return state;
            }
        }
        throw new IllegalArgumentException("Enum type does not exist!");
    }

    public static WeightUnitEnum fromUnit(String unit) {
        unit = unit.toLowerCase();
        for (WeightUnitEnum state : WeightUnitEnum.values()) {
            if (Objects.equals(unit, state.getUnit())) {
                return state;
            }
        }
        throw new IllegalArgumentException("Enum type does not exist!");
    }

    public static WeightUnitEnum fromNameCnOrUnit(String arg) {
        for (WeightUnitEnum state : WeightUnitEnum.values()) {
            if (Objects.equals(arg, state.getNameCn())) {
                return state;
            }
        }
        arg = arg.toLowerCase();
        for (WeightUnitEnum state : WeightUnitEnum.values()) {
            if (Objects.equals(arg, state.getUnit())) {
                return state;
            }
        }
        return null;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.unit;
    }
}
