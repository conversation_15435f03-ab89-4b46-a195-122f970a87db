package com.zsmall.extend.shop.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.zsmall.extend.shop.service.TpsAmazonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 第三方店铺-Amazon授权Controller
 */
@Slf4j
@ConditionalOnExpression("${thirdparty-shop.amazon.enabled:false}")
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/tps/amazon")
public class TpsAmazonAuthorizeController {

    private final long EXPIRE_AUTHORIZE_CODE = 2 * 60 * 3600;

    private final TpsAmazonService tpsAmazonService;

    /**
     * 亚马逊跳转应用授权页
     *
     * @param httpRequest
     * @param httpResponse
     * @throws Exception
     */
    @SaIgnore
    @GetMapping("/toAppAuthorizeUrl/{appid}")
    public void toAmazonAppRedirectUrl(@PathVariable String appid, HttpServletRequest httpRequest, HttpServletResponse httpResponse)
        throws IOException {
        log.info("toAppAuthorizeUrl appid = {}", appid);
        String url = tpsAmazonService.getWebsiteAuthorizeUrl(appid);
        httpResponse.sendRedirect(url);
    }

    /**
     * app 跳转至授权地址
     *
     * @param httpRequest
     * @param httpResponse
     * @throws Exception
     */
    @GetMapping("/toAuthorizeLogin")
    public void appToAuthorizeLogin(HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws Exception {
        String state = "";
        Map<String, String> map = new HashMap<>();
        Enumeration<String> parameterNames = httpRequest.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String key = parameterNames.nextElement();
            String value = httpRequest.getParameter(key);

            if ("state".equals(key)) {
                state = value;
            }
            map.put(key, value);
        }
        log.info("toAuthorizeLogin map = {}", JSONUtil.toJsonStr(map));

        // 判断当前授权重定向地址是否真实存在，如果不存在，则报错。
        boolean hasKey = tpsAmazonService.checkAuthorizeState(state);
        if (!hasKey) {
            httpResponse.setStatus(HttpStatus.HTTP_FORBIDDEN);
            return;
        }
        String redirectUrl = tpsAmazonService.buildCallbackUrl(map);

        httpResponse.sendRedirect(redirectUrl);
    }

    /**
     * app 接收授权重定向，获取授权信息
     *
     * @param httpRequest
     * @param httpResponse
     * @throws Exception
     */
    @GetMapping("/toWebsiteLoginUrl")
    public void toWebsiteLoginUrl(HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws Exception {
        String state = "";
        Map<String, String> map = new HashMap<>();
        Enumeration<String> parameterNames = httpRequest.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String key = parameterNames.nextElement();
            String value = httpRequest.getParameter(key);

            if ("state".equals(key)) {
                state = value;
            }
            map.put(key, value);
        }
        log.info("toWebsiteLoginUrl map = {}", JSONUtil.toJsonStr(map));

        // 判断当前授权重定向地址是否真实存在，如果不存在，则报错。
        boolean hasKey = tpsAmazonService.checkAuthorizeState(state);
        if (!hasKey) {
            httpResponse.setStatus(HttpStatus.HTTP_FORBIDDEN);
            return;
        }

        String redirectUrl = tpsAmazonService.buildWebsiteUrl(map);
        httpResponse.sendRedirect(redirectUrl);
    }

}
