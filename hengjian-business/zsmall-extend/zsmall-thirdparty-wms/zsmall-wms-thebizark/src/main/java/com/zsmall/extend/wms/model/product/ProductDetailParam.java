package com.zsmall.extend.wms.model.product;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 产品详情
 */
@Getter
@Setter
@Accessors(chain = true)
public class ProductDetailParam {

    /**
     * 产品SKU
     */
    private String sku;

    /**
     * 计划数量
     */
    private Integer qty;

    /**
     * 装箱数量
     */
    private Integer qtyPacked;

    /**
     * 接收数量
     */
    private Integer qtyReceived;

    /**
     * 接收产品中可售数量
     */
    private Integer qtyReceivedSaleable;

    /**
     * 接收产品中不可售数量
     */
    private Integer qtyReceivedUnsaleable;

}
