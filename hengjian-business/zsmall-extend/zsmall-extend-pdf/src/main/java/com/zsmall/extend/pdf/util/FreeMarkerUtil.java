package com.zsmall.extend.pdf.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import com.zsmall.extend.pdf.dto.FontDTO;
import com.zsmall.extend.pdf.dto.GeneratePDFDTO;
import com.zsmall.extend.pdf.exception.FreeMarkerException;
import freemarker.cache.FileTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateExceptionHandler;
import lombok.Cleanup;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;
import java.util.List;
import java.util.Map;

/**
 * Created by fgm on 2017/4/22. FREEMARKER 模板工具类
 */
public class FreeMarkerUtil {

  private static final String WINDOWS_SPLIT = "\\";

  private static final String UTF_8 = "UTF-8";

  private static Map<String, FileTemplateLoader> fileTemplateLoaderCache = Maps.newConcurrentMap();

  private static Map<String, Configuration> configurationCache = Maps.newConcurrentMap();

  public static Configuration getConfiguration(String templateFilePath) {
    if (null != configurationCache.get(templateFilePath)) {
      return configurationCache.get(templateFilePath);
    }
    Configuration config = new Configuration(Configuration.VERSION_2_3_25);
    config.setDefaultEncoding(UTF_8);
    config.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
    config.setLogTemplateExceptions(false);
    FileTemplateLoader fileTemplateLoader = null;
    if (null != fileTemplateLoaderCache.get(templateFilePath)) {
      fileTemplateLoader = fileTemplateLoaderCache.get(templateFilePath);
    }
    try {
      fileTemplateLoader = new FileTemplateLoader(new File(templateFilePath));
      fileTemplateLoaderCache.put(templateFilePath, fileTemplateLoader);
    } catch (IOException e) {
      throw new FreeMarkerException("fileTemplateLoader init error!", e);
    }
    config.setTemplateLoader(fileTemplateLoader);
    configurationCache.put(templateFilePath, config);
    return config;

  }

  /**
   * 生成PDF
   * @param generatePDFDTO
   * @throws Exception
   */
  public static void generatePDFByBean(GeneratePDFDTO generatePDFDTO) throws Exception {
    Map<String, Object> data = generatePDFDTO.getData();
    String fileName = generatePDFDTO.getFileName();
    String templatePath = generatePDFDTO.getTemplatePath();
    String content = freeMarkerRender(data, templatePath, fileName);

    String destinationPath = generatePDFDTO.getDestinationPath();
    List<FontDTO> fontDTOList = generatePDFDTO.getFontDTOList();
    createPdf(content, destinationPath, fontDTOList);
  }

  /**
   * freemarker渲染html
   */
  /**
   * 模板渲染
   * @param data 数据
   * @param templatePath 模板路径
   * @param fileName 文件名
   * @return
   */
  public static String freeMarkerRender(Map<String, Object> data, String templatePath, String fileName) {
    Configuration freemarkerCfg = getConfiguration(templatePath);

    try {
      @Cleanup Writer out = new StringWriter();
      // 获取模板,并设置编码方式
      Template template = freemarkerCfg.getTemplate(fileName);
      template.setEncoding("UTF-8");
      // 合并数据模型与模板
      template.process(data, out); //将合并后的数据和模板写入到流中，这里使用的字符流
      out.flush();
      return out.toString();
    } catch (Exception e) {
      e.printStackTrace();
      throw new FreeMarkerException();
    }
  }

  private static void createPdf(String content, String dest, List<FontDTO> fontDTOList) throws Exception {
    ITextRenderer render = new ITextRenderer();

    //默认中文字体支持，防止中文乱码或者不显示
    if (CollUtil.isNotEmpty(fontDTOList)) {
      ITextFontResolver fontResolver = render.getFontResolver();
      for (FontDTO fontDTO : fontDTOList) {
        fontResolver.addFont(fontDTO.getPath(), fontDTO.getEncoding(), fontDTO.isEmbedded());
      }
    }

    // 解析html生成pdf
    render.setDocumentFromString(content);
    render.layout();
    render.createPDF(new FileOutputStream(dest));
    render.finishPDF();
  }

  /**
   * File to 64bit Str
   *
   * @param file
   * @return
   */
  public static String fileToBase64Str(File file) {
    byte[] data = null;
    InputStream inputStream = null;
    if (file != null) {
      try {
        inputStream = new FileInputStream(file);
        data = new byte[inputStream.available()];
        inputStream.read(data);
      } catch (Exception e) {
        e.printStackTrace();
      } finally {
        try {
          inputStream.close();
        } catch (IOException e) {
          e.printStackTrace();
        }
      }
      return Base64.encode(data);
    }
    return null;
  }


}
