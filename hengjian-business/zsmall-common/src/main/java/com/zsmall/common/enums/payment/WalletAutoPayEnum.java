package com.zsmall.common.enums.payment;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025年1月24日  14:39
 * @description:
 */
@JsonSerialize(using = WalletAutoPayEnumSerializer.class)
public enum WalletAutoPayEnum implements IEnum<Integer> {

    /**
     * 0-关闭
     */
    CLOSE(0),

    /**
     * 1-开启
     */
    OPEN(1),

    ;

    private Integer code;

    WalletAutoPayEnum(Integer code) {
        this.code = code;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    public static WalletAutoPayEnum fromCode(Integer code) {
        for (WalletAutoPayEnum state : WalletAutoPayEnum.values()) {
            if (code.equals(state.getValue())) {
                return state;
            }
        }
        throw new IllegalArgumentException("Enum type does not exist!");
    }

    @Override
    public String toString() {
        return String.valueOf(this.getValue());
    }

}

class WalletAutoPayEnumSerializer extends JsonSerializer<WalletAutoPayEnum> {
    @Override
    public void serialize(WalletAutoPayEnum value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeNumber(value.getValue());
    }
}
