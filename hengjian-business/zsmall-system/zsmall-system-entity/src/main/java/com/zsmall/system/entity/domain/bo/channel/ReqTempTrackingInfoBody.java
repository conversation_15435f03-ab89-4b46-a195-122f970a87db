package com.zsmall.system.entity.domain.bo.channel;

import com.zsmall.common.domain.bo.TrackingNoBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-09
 **/
@Data
@NoArgsConstructor
@Schema(name = "请求信息-临时表物流跟踪信息")
public class ReqTempTrackingInfoBody {

    @Schema(title = "临时订单编号")
    private String tempOrderNo;

    @Schema(title = "是否选择第三方物流")
    private Boolean thirdBilling;

    @Schema(title = "第三方物流商（UPS，FedEx）")
    private String carrier;

    @Schema(title = "物流服务名称")
    private String logisticsServiceName;

    @Schema(title = "第三方物流发货商账号")
    private String logisticsAccount;

    @Schema(title = "第三方发货商账号邮编")
    private String logisticsAccountZipCode;

    @Schema(title = "物流类型（PickUp-自提，DropShipping-代发商品）")
    private String logisticsType;

    @Schema(title = "仓库编码")
    private String warehouseCode;

    @Schema(title = "跟踪单号")
    private String trackingNo;

    @Schema(title = "物流跟踪单号集合")
    private List<TrackingNoBo> trackingInfoList;

    @Schema(title = "ItemNo")
    private String itemNo;

    @Schema(title = "购物车订单")
    private String isOneLink;

}
