package com.zsmall.extend.shop.tiktok.job.factory;


import com.zsmall.common.handler.AbstractThirdBusinessHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/3 14:20
 */
//@Component
//
//public class ThirdBusinessFactory {
//    @Resource
//    private Map<String, AbstractThirdBusinessHandler> strategyMap;
//
//    public AbstractThirdBusinessHandler getInvokeStrategy(String strategyName){
//        return strategyMap.get(strategyName);
//    }
//}
