package com.zsmall.extend.shop.tiktok.job.factory;

import com.zsmall.common.handler.AbstractOrderHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/30 10:52
 */
@SuppressWarnings("rawtypes")
@Component
public class ThirdOrderApiFactory {

    @Resource
    private Map<String, AbstractOrderHandler> strategyMap;

    public AbstractOrderHandler getInvokeStrategy(String strategyName){
        return strategyMap.get(strategyName);
    }
}
