package com.zsmall.system.entity.domain.bo.transaction;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TransactionOrderRecordBo {

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 查询下单时间范围
     */
    private List<String> ordersDates;

    /**
     * 查询交易时间范围
     */
    private List<String> tradingDates;
    /**
     * 交易单号
     */
    private String transactionNo;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 租户ID
     */
    private String tenantId;

}
