package com.zsmall.order.entity.domain.bo;

import cn.hutool.json.JSONObject;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.order.entity.domain.OrderExtraInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 主订单额外信息业务对象 order_extra_info
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderExtraInfo.class, reverseConvertGenerate = false)
public class OrderExtraInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 主订单编号
     */
    @NotBlank(message = "主订单编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderNo;

    /**
     * 信息正文
     */
    @NotBlank(message = "信息正文不能为空", groups = {AddGroup.class, EditGroup.class})
    private JSONObject content;

    /**
     * 信息实体名
     */
    @NotBlank(message = "信息实体名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String entityName;

    /**
     * 信息类型
     */
    @NotBlank(message = "信息类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String infoType;


}
