package com.zsmall.system.entity.domain.bo.receipt;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.system.entity.domain.TenantReceiptAccountAttachment;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 收款账户附件业务对象 tenant_receipt_account_attachment
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantReceiptAccountAttachment.class, reverseConvertGenerate = false)
public class TenantReceiptAccountAttachmentBo extends NoDeptBaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 收款账户id
     */
    @NotNull(message = "收款账户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long receiptAccountId;

    /**
     * 存储对象主键
     */
    @NotNull(message = "存储对象主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ossId;

    /**
     * 附件名称
     */
    @NotBlank(message = "附件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentName;

    /**
     * 附件原名
     */
    @NotBlank(message = "附件原名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    @NotBlank(message = "附件后缀不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    @NotBlank(message = "附件存放路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    @NotBlank(message = "附件展示地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    @NotNull(message = "附件排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long attachmentSort;

    /**
     * 附件类型
     */
    @NotBlank(message = "附件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attachmentType;


}
