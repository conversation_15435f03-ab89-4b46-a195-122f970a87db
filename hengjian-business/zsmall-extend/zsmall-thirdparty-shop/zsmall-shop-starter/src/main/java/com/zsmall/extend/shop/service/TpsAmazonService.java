package com.zsmall.extend.shop.service;

import com.zsmall.extend.shop.model.vo.AmznAccessToken;

import java.io.IOException;
import java.util.Map;

/**
 * Amazon接口
 */
public interface TpsAmazonService {

    /**
     * 获取应用重定向地址
     * @param appConfigId
     * @return
     */
    String getAmazonAppRedirectUrl(String appConfigId);

    String getWebsiteAuthorizeUrl(String appid);

    boolean checkAuthorizeState(String state);

    void addAuthorizeState2Redis(String state, String appBizId);

    /**
     * 构造回调地址
     * @param map
     * @return
     */
    String buildCallbackUrl(Map<String, String> map);

    /**
     * 构造网站URL
     * @param map
     * @return
     */
    String buildWebsiteUrl(Map<String, String> map);

    /**
     * 根据code，获取token
     * @param appBizId
     * @param code
     * @return
     * @throws IOException
     */
    AmznAccessToken getToken(String appBizId, String code) throws IOException;
}
