package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 属性类型枚举
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
public enum AttributeTypeEnum implements IEnum<String> {

    /**
     * 商品可选规格
     */
    OptionalSpec,

    /**
     * 商品通用规格
     */
    GenericSpec,

    /**
     * 商品特色
     */
    Feature,

    ;

    @Override
    public String getValue() {
        return this.name();
    }

}
