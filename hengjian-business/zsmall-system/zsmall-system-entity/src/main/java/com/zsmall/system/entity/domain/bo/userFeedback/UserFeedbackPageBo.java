package com.zsmall.system.entity.domain.bo.userFeedback;

import com.hengjian.common.mybatis.core.domain.QueryBaseEntity;
import lombok.Data;

/**
 * 请求体-分页查询用户反馈
 *
 * <AUTHOR>
 * @date 2023/9/11
 */
@Data
public class UserFeedbackPageBo extends QueryBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 反馈日期
     */
    private String feedbackDate;

    /**
     * 反馈类型
     */
    private String feedbackType;

    /**
     * 答复状态
     */
    private String replyState;

}
