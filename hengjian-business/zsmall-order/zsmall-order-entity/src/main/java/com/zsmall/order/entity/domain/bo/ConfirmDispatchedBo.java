package com.zsmall.order.entity.domain.bo;

import lombok.Data;

import java.util.List;

/**
 * 主订单物流信息业务对象 order_logistics_info
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
public class ConfirmDispatchedBo {

    /**
     * 子订单编号
     */
    private String orderItemNo;
    /**
     * 承运商
     */
    private String carrier;

    /**
     * 物流跟踪单号集合
     */
    private List<TrackingNoDTO> trackingInfoList;


    @Data
    public static class TrackingNoDTO {

        /**
         * 承运商
         */
        private String carrier;

        /**
         * 跟踪单号
         */
        private String trackingNo;

    }

}
