package com.zsmall.system.entity.domain.bo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-08-09
 **/
@Data
@NoArgsConstructor
@Schema(name = "请求信息-临时单文件删除")
public class ReqTempFileDeleteBody {

    @Schema(title = "临时订单编号")
    private String tempOrderNo;

    @Schema(title = "文件类型：ShippingLabel-快递标签，OrderAttachment-订单附件")
    private String fileType;

}
