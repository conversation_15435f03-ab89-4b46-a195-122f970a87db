package com.zsmall.extend.wms.model.product;

import cn.hutool.core.annotation.Alias;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 响应输出 - 渠道商品
 */
@Getter
@Setter
@Accessors(chain = true)
public class OutChannelProduct {

    /**
     * 平台账号（如 office-US）
     */
    private String channel;

    /**
     * 0:单个产品
     * 1:组合产品
     * 2:虚拟产品
     */
    @Alias("sku_type")
    private Integer skuType;

    /**
     * erp系统中的 sku (组织产品下，此erpSku是拼接的ErpSku,具体的erpSku在 bundle_product_item下 )
     */
    private String erpsku;

    /**
     * 平台上售卖的sku
     */
    @Alias("seller_sku")
    private String sellSku;

    /**
     * 状态 'Y':启用，'N'停用
     */
    private String status;

    /**
     * Hit Shelve: 上架，OffShelve:下架，Not Sell: 不再售卖
     */
    @Alias("sale_state")
    private String saleState;

    /**
     * 创建时间
     */
    @Alias("created_at")
    private String createdAt;

    /**
     * 更新时间
     */
    @Alias("created_at")
    private String updatedAt;

    /**
     * 组合产品信息(只有组合产品，此项才有值 )
     */
    @Alias("bundle_product_item")
    private List<OutBundleProduct> bundleProductItem;

}
