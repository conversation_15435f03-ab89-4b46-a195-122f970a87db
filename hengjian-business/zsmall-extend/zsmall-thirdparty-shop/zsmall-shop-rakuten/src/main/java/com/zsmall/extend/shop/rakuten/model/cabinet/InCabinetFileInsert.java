package com.zsmall.extend.shop.rakuten.model.cabinet;

import lombok.Data;

import java.io.Serializable;

/**
 * 请求体-上传文件到文件柜
 *
 * <AUTHOR>
 * @date 2023/10/24
 */
@Data
public class InCabinetFileInsert implements Serializable {

    private static final long serialVersionUID = 1L;

    private Result result;

    @Data
    public class Result {

        private Status status;

        private CabinetFileInsertResult cabinetFileInsertResult;

    }

    @Data
    public class Status {

        private String interfaceId;
        private String systemStatus;
        private String message;
        private String requestId;
        private String requests;

    }

    @Data
    public class CabinetFileInsertResult {

        private int resultCode;
        private int fileId;

    }

}
