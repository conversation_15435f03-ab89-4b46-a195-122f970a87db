package com.zsmall.extend.shop.amazon.job.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.amazon.SellingPartnerAPIAA.LWAAccessTokenCache;
import com.amazon.SellingPartnerAPIAA.LWAException;
import com.amazon.client.api.TokensApi;
import com.amazon.client.enums.RdtDataElementType;
import com.amazon.client.enums.RestrictedDataTokenType;
import com.amazon.client.invoker.ApiException;
import com.amazon.client.invoker.ApiResponse;
import com.amazon.client.model.CreateRestrictedDataTokenRequest;
import com.amazon.client.model.CreateRestrictedDataTokenResponse;
import com.amazon.client.model.RestrictedResource;
import com.amazon.client.model.builder.CreateRestrictedDataTokenBuilder;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.extend.shop.amazon.bean.AmazonAppBean;
import com.zsmall.extend.shop.amazon.kit.AmazonSpApiKit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class AmazonTokenApiSupport {

    /**
     * 亚马逊令牌-缓存关键字
     * AMAZON_TOKENS_{sellerId}_{restrictedPath-受限请求地址路径}
     */
    private final static String KEY_TOKENS = GlobalConstants.GLOBAL_REDIS_KEY + "job:amazon:rdtoken:%s:%s";
    /**
     * 设置超时前30秒就提前获取token
     */
    private final static Integer SECONDS_AHEAD = 30;

    private final LWAAccessTokenCache lwaAccessTokenCache;


    /**
     * 请求Amazon并获取Token
     * @param refreshToken
     * @param createRestrictedDataTokenRequest
     * @return
     */
    public CreateRestrictedDataTokenResponse getTokenWithHttp(AmazonAppBean amazonAppBean, String refreshToken,
                                                              CreateRestrictedDataTokenRequest createRestrictedDataTokenRequest) {
        log.info("getTokenWithHttp parameters = {}", JSONUtil.toJsonStr(createRestrictedDataTokenRequest));

        TokensApi tokensApi = AmazonSpApiKit.createTokensApi(amazonAppBean, lwaAccessTokenCache, refreshToken);

        CreateRestrictedDataTokenResponse tokenWithHttpInfoData = null;
        try {
            ApiResponse<CreateRestrictedDataTokenResponse> dataTokenWithHttpInfo = tokensApi.createRestrictedDataTokenWithHttpInfo(createRestrictedDataTokenRequest);
            log.info("dataTokenWithHttpInfo = {}", JSONUtil.toJsonStr(dataTokenWithHttpInfo));
            tokenWithHttpInfoData = dataTokenWithHttpInfo.getData();
        } catch (ApiException | LWAException e) {
            log.error("Call Amazon getTokens error, " + e.getMessage(), e);
        }
        return tokenWithHttpInfoData;
    }

    /**
     * 获取受限请求token
     * @param amazonAppBean
     * @param sellerId
     * @param refreshToken
     * @return
     */
    public String getOrderToken(AmazonAppBean amazonAppBean, String sellerId, String refreshToken) {
        log.info("getOrderToken parameters = {}, {}", JSONUtil.toJsonStr(amazonAppBean), refreshToken);

        List<String> dataElementTypes =new ArrayList<>();
        dataElementTypes.add(RdtDataElementType.shippingAddress.name());
        CreateRestrictedDataTokenRequest createRestrictedDataTokenRequest =
            new CreateRestrictedDataTokenBuilder()
                .setRestrictedType(RestrictedDataTokenType.RestrictedType.Orders)
                .setRestrictedDataTokenType(RestrictedDataTokenType.Orders.getOrders.name())
                .setParamMap(new HashMap<>())
                .setDataElementTypes(dataElementTypes)
                .build();

        String restrictedDataToken;
        String tokenKey = getTokenKey(sellerId, createRestrictedDataTokenRequest);
        Boolean hasKey = RedisUtils.hasKey(tokenKey);
        if(hasKey) {
            restrictedDataToken = RedisUtils.getCacheObject(tokenKey);
        } else {
            TimeInterval timer = DateUtil.timer();

            CreateRestrictedDataTokenResponse tokenResponse =
                this.getTokenWithHttp(amazonAppBean, refreshToken, createRestrictedDataTokenRequest);

            log.info("CreateRestrictedDataTokenResponse = {}", JSONUtil.toJsonStr(tokenResponse));
            restrictedDataToken = tokenResponse.getRestrictedDataToken();
            if(StrUtil.isNotBlank(restrictedDataToken)) {
                Integer expiresIn = tokenResponse.getExpiresIn();

                if(expiresIn != null && expiresIn > SECONDS_AHEAD) {
                    expiresIn = expiresIn - SECONDS_AHEAD;
                }
                log.info("redis key = {}, value = {}, expiresIn = {}", tokenKey, restrictedDataToken, expiresIn);
                RedisUtils.setCacheObject(tokenKey, restrictedDataToken, Duration.ofSeconds(expiresIn));
            }
            long millSeconds = timer.interval();
            log.info("请求耗时 = {}", millSeconds);
            if(millSeconds < 1000) {
                ThreadUtil.safeSleep(1000);
            }
        }

        return restrictedDataToken;
    }

    /**
     * 获取缓存Key
     * @param sellerId
     * @param createRestrictedDataTokenRequest
     * @return
     */
    private String getTokenKey(String sellerId, CreateRestrictedDataTokenRequest createRestrictedDataTokenRequest) {
        String tokenFirstPath = this.getTokenPath(createRestrictedDataTokenRequest);
        String tokenKey = String.format(KEY_TOKENS, sellerId, tokenFirstPath);
        log.info("getTokenKey tokenKey = {}", tokenKey);
        return tokenKey;
    }

    /**
     * 获取受限列表第一个请求地址
     * @param createRestrictedDataTokenRequest
     * @return
     */
    private String getTokenPath(CreateRestrictedDataTokenRequest createRestrictedDataTokenRequest) {
        String path = "";
        List<RestrictedResource> restrictedResources = createRestrictedDataTokenRequest.getRestrictedResources();
        if(CollUtil.isNotEmpty(restrictedResources)) {
            RestrictedResource restrictedResource = restrictedResources.get(0);
            path = restrictedResource.getPath();
        }
        log.info("getTokenPath path = {}", path);
        return path;
    }

}
