package com.zsmall.product.entity.domain.bo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用参数-商品分类信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-商品分类信息")
public class ProductCategoryBody {

    @Schema(title = "id")
    private Long id;

    @Schema(title = "展开项的id集合")
    private List<Long> showIds;

    @Schema(title = "分类名称")
    private String name;

    @Schema(title = "分类中文名称")
    private String nameCN;

    @Schema(title = "分类中文名称")
    private String name_zh_CN;

    @Schema(title = "分类英文名称")
    private String name_en_US;

    @Schema(title = "类目等级")
    private Integer level;

    @Schema(title = "savePath")
    private String savePath = "";

    @Schema(title = "showUrl")
    private String showUrl;

    @Schema(title = "iconPath")
    private String iconPath = "";

    @Schema(title = "iconUrl")
    private String iconUrl;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "有效状态")
    private String categoryState;

    @Schema(title = "父级id")
    private Long parentId;

    @Schema(title = "父级id集合（集合下标对应层级，如parentIdList[1]的值为第一层级id，parentIdList[2]的值为第二层级id；parentIdList[0]无意义）")
    private List<Long> parentIdList;

    @Schema(title = "子分类")
    private List<ProductCategoryBody> children = null;

    @Schema(title = "有子层级")
    private Boolean hasChildren = false;

    @Schema(title = "是否禁用勾选")
    private Boolean disabled = false;

    @Schema(title = "分类所属渠道:Amazon;Shopify;Wayfair;OneLink")
    private String channelType;

    @Schema(title = "分类下商品数量")
    private Integer items;

    @Schema(title = "是否展开显示")
    private Boolean show = false;

    @Schema(title = "是否编辑状态")
    private Boolean isEdit = false;

    @Schema(title = "属性集合")
    private List<CategoryAttributesBody> attributesBodies;

    @Schema(title = "不添加当前层级选项")
    private Boolean notAddCurrentOptions;

}
