package com.zsmall.order.biz.anno.annotaion;

import com.zsmall.common.enums.OpenApiEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/13 14:28
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface OpenApiParamCheck {
    OpenApiEnum value() default OpenApiEnum.CREATE_ORDER;
}
