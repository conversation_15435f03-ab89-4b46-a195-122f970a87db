package com.zsmall.order.biz.test.job;

import cn.hutool.core.date.StopWatch;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.order.biz.test.service.WarehouseServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/18 17:51
 */
@Slf4j
@Service
public class WarehouseHandlerJob {
    @Resource
    private  WarehouseServiceV2 warehouseServiceV2;

    /**
     * 功能描述：提取库存作业---全量产品库存拉取
     *
     * <AUTHOR>
     * @date 2024/03/18
     */
    @XxlJob("pullInventoryV2Job")
    public void pullInventoryV2Job() {
        // 30分钟执行一次的cron
        log.info("仓库库存拉取任务开始");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        warehouseServiceV2.pullInventoryJob();

        stopWatch.stop();
        log.info("仓库库存拉取任务结束,用时信息:{}",stopWatch.prettyPrint());
    }
}
