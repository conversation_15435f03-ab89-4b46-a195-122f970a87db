package com.zsmall.extend.shop.amazon.job.support;

import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.extend.shop.amazon.bean.AmazonAppBean;
import com.zsmall.extend.shop.amazon.business.entity.domain.AmazonAppConfig;
import com.zsmall.extend.shop.amazon.business.entity.iservice.IAmazonAppConfigService;

import java.time.Duration;

public class JobBaseSupport {
    protected IAmazonAppConfigService amazonAppConfigService = SpringUtils.getBean(IAmazonAppConfigService.class);

    protected AmazonAppBean buildAppBean(String amznAppId) {
        String key = GlobalConstants.GLOBAL_REDIS_KEY + amznAppId;
        Boolean hasKey = RedisUtils.hasKey(key);

        AmazonAppBean amazonAppBean = null;
        if(hasKey) {
            String cacheString = RedisUtils.getCacheObject(key);
            amazonAppBean = JSONUtil.toBean(cacheString, AmazonAppBean.class);
        } else {
            AmazonAppConfig amazonAppConfig = amazonAppConfigService.selectConfigByAppId(amznAppId);
            amazonAppBean = buildAppBean(amazonAppConfig);

            RedisUtils.setCacheObject(key, JSONUtil.toJsonStr(amazonAppBean), Duration.ofMinutes(10));
        }

        return amazonAppBean;
    }

    /**
     * 构造亚马逊APP实体
     * @param amazonAppConfig
     * @return
     */
    protected AmazonAppBean buildAppBean(AmazonAppConfig amazonAppConfig) {
        AmazonAppBean amazonAppBean = new AmazonAppBean();
        amazonAppBean.setAppId(amazonAppBean.getAppId());

        String endpointString = amazonAppConfig.getEndpoint();
        String authorizeInfoString = amazonAppConfig.getAuthorizeInfo();

        AmazonAppBean.Endpoint beanEndpoint = JSONUtil.toBean(endpointString, AmazonAppBean.Endpoint.class);
        AmazonAppBean.AuthorizeInfo beanAuthorizeInfo = JSONUtil.toBean(authorizeInfoString, AmazonAppBean.AuthorizeInfo.class);
        amazonAppBean.setEndpoint(beanEndpoint);
        amazonAppBean.setAuthorizeInfo(beanAuthorizeInfo);
        return amazonAppBean;
    }

}
