package com.zsmall.order.entity.domain.bo.order;

import com.zsmall.order.entity.domain.vo.order.TrackingInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求参数-改变订单详情信息
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ChangeOrderDetailsBo {
    /**
     *订单编号
     */
    private String orderNo;
    /**
     *子订单号
     */
    private String orderItemNo;
    /**
     *发货方式
     */
    private String logisticsType;
    /**
     *商品数量
     */
    private Integer num;
    /**
     * 承运商
     */
    private String carrier;
    /**
     * 物流账户
     */
    private String logisticsAccount;
    /**
     * 快递单号列表
     */
    private List<TrackingInfo> trackingInfoList;
    /**
     * 物流服务名称
     */
    private String logisticsServiceName;

}
