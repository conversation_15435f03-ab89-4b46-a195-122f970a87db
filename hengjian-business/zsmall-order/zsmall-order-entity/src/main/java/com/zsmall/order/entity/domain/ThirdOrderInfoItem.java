package com.zsmall.order.entity.domain;

import com.hengjian.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 第三方订单信息子对象 third_order_info_item
 *
 * <AUTHOR> Li
 * @date 2024-08-05
 */
@Data
@TableName("third_order_info_item")
@Accessors(chain = true)
public class ThirdOrderInfoItem implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    private String tenantId;

    /**
     * 店铺名称
     */
    private String accountId;

    /**
     * 第三方订单号
     */
    private String orderNum;

    /**
     * sku
     */
    private String sku;

    /**
     * asin
     */
    private String asin;

    /**
     * 标题
     */
    private String title;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 小计
     */
    private BigDecimal subtotal;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 发货数量
     */
    private Integer shipedQuantity;

    /**
     * shipping_price
     */
    private BigDecimal shippingPrice;

    /**
     * shipping_tax_price
     */
    private BigDecimal shippingTaxPrice;

    /**
     * shipping_discount_price
     */
    private BigDecimal shippingDiscountPrice;

    /**
     * tax_price
     */
    private BigDecimal taxPrice;

    /**
     * promotion_discount_price
     */
    private BigDecimal promotionDiscountPrice;

    /**
     * gift_wrap_price
     */
    private BigDecimal giftWrapPrice;

    /**
     * gift_wrap_tax_price
     */
    private BigDecimal giftWrapTaxPrice;

    /**
     * 子订单行
     */
    private String channelOrderItemId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    // 2025-02-12 新增字段 amazonVC
    private String is_back_order_allowed;
    private String unit_of_measure;
    private String unit_size;
    // 2025-02-27 新增字段 amazonVC
    private String taxCurrencyCode;
    private BigDecimal listingPrice;

    // 2025-02-12 新增字段 amazonSC
    private String promotion_ids;
    private String productSkuCode;
    private String ordernum;

    // 2025-02-13 新增字段 temu
    private BigDecimal price;
    private BigDecimal sales_total_amount;
    private String sku_name;
    private Integer canceled_quantity_before_shipment;
}
