<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.extend.shop.amazon.job.entity.mapper.JobAmznFeedSyncMapper">

<!--    <resultMap id="BaseResultMap" type="com.zsmall.extend.shop.amazon.job.entity.domain.JobAmznFeedSync">-->
<!--            <id property="id" column="id" jdbcType="BIGINT"/>-->
<!--            <result property="amznAppId" column="amzn_app_id" jdbcType="VARCHAR"/>-->
<!--            <result property="sellerId" column="seller_id" jdbcType="VARCHAR"/>-->
<!--            <result property="marketplaceId" column="marketplace_id" jdbcType="VARCHAR"/>-->
<!--            <result property="refreshToken" column="refresh_token" jdbcType="VARCHAR"/>-->
<!--            <result property="syncStatus" column="sync_status" jdbcType="VARCHAR"/>-->
<!--            <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>-->
<!--            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>-->
<!--            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        id,amzn_app_id,seller_id,-->
<!--        marketplace_id,refresh_token,sync_status,-->
<!--        error_message,create_time,update_time-->
<!--    </sql>-->
</mapper>
