package com.zsmall.product.entity.domain.bo.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/7 11:03
 */
@Data
public class MemberLevelQueryBo {


    @ApiModelProperty(value = "等级名称")
    private String levelName;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty("经销商id")
    private String ruleFollowerTenantId;
    @ApiModelProperty("供应商id")
    private String ruleCustomizerTenantId;

    @ApiModelProperty(value = "等级名称")
    private String levelId;
}
