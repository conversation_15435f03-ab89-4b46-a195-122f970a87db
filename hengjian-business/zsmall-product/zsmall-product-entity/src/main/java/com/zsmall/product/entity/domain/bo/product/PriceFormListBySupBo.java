package com.zsmall.product.entity.domain.bo.product;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 请求参数-供应商商品价格
 */
@Data
@NoArgsConstructor
public class PriceFormListBySupBo {

    /**
     * 价格集合
     */
    private List<PriceFormBySupBo> priceList;

    /**
     * 价格信息
     */
    @Data
    public static class PriceFormBySupBo {

        /**
         * 价格主键
         */
        private Long id;
        /**
         *
         */
        private String itemNo;

        /**
         * 站点id
         */
        private Long siteId;
        /**
         * 产品单价
         */
        private BigDecimal originalUnitPrice;
        /**
         * 操作费
         */
        private BigDecimal originalOperationFee;
        /**
         * 尾程派送费
         */
        private BigDecimal originalFinalDeliveryFee;
        /**
         * 建议零售价
         */
        private BigDecimal msrp;
    }
}
