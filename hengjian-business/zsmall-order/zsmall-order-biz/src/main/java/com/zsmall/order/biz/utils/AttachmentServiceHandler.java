package com.zsmall.order.biz.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.service.impl.SysOssServiceImpl;
import com.zsmall.common.domain.dto.AttachInfo;
import com.zsmall.common.domain.dto.SaleOrderDetailDTO;
import com.zsmall.common.enums.common.CarrierTypeEnum;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.order.biz.service.DistributorOrderService;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.OrderUpdateBo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/11/14 16:43
 */
@Slf4j
@Component
public class AttachmentServiceHandler {
    @Resource
    private DistributorOrderService distributorOrderService;
    @Resource
    private SysOssServiceImpl sysOssService;
    @Resource
    private RedissonClient redissonClient;
    /**
     * 功能描述：oss上传方法,
     * 1.saleOrderDetails.carrier 不能为空
     * 2.attachInfo.type 不能为空
     *
     * @param orders           订单
     * @param attachInfo       附加信息
     * @param saleOrderDetails 销售订单详细信息
     * @param orderExtendId    订单扩展id
     * @param i                我
     * <AUTHOR>
     * @date 2024/11/14
     */
    public void ossUploadMethod(Orders orders, AttachInfo attachInfo, SaleOrderDetailDTO saleOrderDetails,
                         String orderExtendId, int i) {
        Integer fileType = attachInfo.getFileType();
        OrderAttachmentTypeEnum typeEnum = OrderAttachmentTypeEnum.getByCode(fileType);
        String carrier = saleOrderDetails.getCarrier();
        // 按照承运商类型区分,如果是 LTL的 附件只走一个
        // LTL BOL附件只走一个,label会有多个,但是多个订单用一个附件的ossId
        if (CarrierTypeEnum.LTL.getValue().equals(carrier)) {
            // bol 只上传一次 通过唯一标识来判断
            // 考虑直接上锁,同一个行id,就上传一次
            // 从key里拿value
            String lockKey =null;
            String ossKey =null;
            if(fileType==0){
                lockKey =  "oss"+":"+"ShippingLabel:"+i+":"+orderExtendId;
                ossKey = "oss:key"+":"+"ShippingLabel:"+i+":"+ orderExtendId;
            }
            if(fileType==2){
                lockKey =  "oss"+":"+"BOL:"+ orderExtendId;
                ossKey = "oss:key"+":"+"BOL:"+ orderExtendId;
            }

            if(fileType==4){
                lockKey =  "oss"+":"+"CartonLabel:"+i+ orderExtendId;
                ossKey = "oss:key"+":"+"CartonLabel:"+ orderExtendId;
            }
            if(fileType==5){
                lockKey =  "oss"+":"+"PalletLabel:"+i+ orderExtendId;
                ossKey = "oss:key"+":"+"PalletLabel:"+i+ orderExtendId;
            }
            if(fileType==6){
                lockKey =  "oss"+":"+"ItemLabel:"+i+ orderExtendId;
                ossKey = "oss:key"+":"+"ItemLabel:"+i+ orderExtendId;
            }
            if(fileType==7){
                lockKey =  "oss"+":"+"Other:"+i+ orderExtendId;
                ossKey = "oss:key"+":"+"Other:"+i+ orderExtendId;
            }

            RLock lock = redissonClient.getLock(lockKey);
            SysOssVo sysOssVo = null;
            boolean locked = false;
            try {
                locked = lock.tryLock(10, TimeUnit.MINUTES);
                if (locked) {
                    try {
                        RBucket<String> bucket = redissonClient.getBucket(ossKey);
                        String value = bucket.get();
                        if (ObjectUtil.isEmpty(value)) {
//                                    sysOssVo = sysOssService.downloadPdfNotAsyncForOpen(attachInfo.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
                            sysOssVo = sysOssService.downloadPdfNotAsync(attachInfo.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
                            value = sysOssVo.getOssId()+"-"+sysOssVo.getSavePath();
                            bucket.set(value, 20, TimeUnit.MINUTES);
                        } else {
                            String[] parts = value.split("-");
                            Long ossId  = Long.valueOf( parts[0]);
                            String savePath = parts[1];
                            // 还要适配多附件的情况
                            sysOssVo = sysOssService.getById(ossId);
                            sysOssVo.setSavePath(savePath);
                        }
                    } finally {
                        lock.unlock();
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                throw new RuntimeException("Lock acquisition interrupted", e);
            }
            OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo,typeEnum);
            distributorOrderService.uploadShippingLabelNotBatch(bo);
        }else {
            // 其他类型只会有一个
            log.info("附件信息:{}", JSONUtil.toJsonStr(attachInfo));
//            SysOssVo sysOssVo = sysOssService.downloadPdfNotAsyncForOpen(attachInfo.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
                SysOssVo sysOssVo = sysOssService.downloadPdfNotAsync(attachInfo.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());

            if(ObjectUtil.isEmpty(sysOssVo)){
                throw new RuntimeException("附件信息异常:"+ attachInfo.getUrl());
            }
            OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo);
            bo.setFileTypeEnum(typeEnum);
            // 实际上传完了以后已经有了文件了 不需要再走上传的逻辑了
            distributorOrderService.uploadShippingLabelNotBatch(bo);
        }
    }
}
