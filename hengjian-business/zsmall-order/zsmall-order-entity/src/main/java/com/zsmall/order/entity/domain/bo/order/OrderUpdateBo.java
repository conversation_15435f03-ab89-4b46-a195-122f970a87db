package com.zsmall.order.entity.domain.bo.order;

import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * 请求体-临时订单更新
 *
 * <AUTHOR>
 * @date 2023/6/12
 */
@Data
@NoArgsConstructor
public class OrderUpdateBo {

    /**
     * 临时订单号（都需要）
     */
    private String orderNo;

    /**
     * 文件（上传快递标签、上传订单附件需要）
     */
    private MultipartFile multipartFile;

    private OrderAttachmentTypeEnum fileTypeEnum;

    private boolean isUpload =false;

    private SysOssVo sysOssVo;

    public OrderUpdateBo(String orderNo) {
        this.orderNo = orderNo;
    }

    public OrderUpdateBo(String orderNo, MultipartFile multipartFile, boolean isUpload , SysOssVo sysOssVo) {
        this.orderNo = orderNo;
        this.multipartFile = multipartFile;
        this.isUpload = isUpload;
        this.sysOssVo = sysOssVo;
    }

    public OrderUpdateBo(String orderNo, MultipartFile multipartFile, boolean isUpload , SysOssVo sysOssVo,OrderAttachmentTypeEnum fileTypeEnum) {
        this.orderNo = orderNo;
        this.multipartFile = multipartFile;
        this.isUpload = isUpload;
        this.sysOssVo = sysOssVo;
        this.fileTypeEnum = fileTypeEnum;
    }
}
