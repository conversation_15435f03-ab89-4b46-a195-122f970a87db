package com.zsmall.extend.shop.amazon.job.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncFeedFulfillmentItem;
import com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncFeedFulfillmentItemMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【amzn_sync_feed_fulfillment_item(亚马逊同步订单履约-子订单信息)】的数据库操作Service实现
* @createDate 2023-11-03 11:52:40
*/
@Service
public class IAmznSyncFeedFulfillmentItemServiceImpl extends ServiceImpl<AmznSyncFeedFulfillmentItemMapper, AmznSyncFeedFulfillmentItem> {

}




