package com.zsmall.system.entity.domain.bo.userFeedback;

import lombok.Data;

import java.io.Serializable;

/**
 * 请求体-用户反馈附件
 *
 * <AUTHOR>
 * @date 2023/9/11
 */
@Data
public class UserFeedbackAttachmentBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 对象存储主键
     */
    private String ossId;
    /**
     * 附件原名
     */
    private String attachmentName;
    /**
     * 附件存储路径
     */
    private String attachmentSavePath;
    /**
     * 附件展示URL
     */
    private String attachmentShowUrl;
    /**
     * 附件排序
     */
    private String attachmentSort;
    /**
     * 附件类型
     */
    private String attachmentType;

}
