package com.zsmall.system.entity.domain.bo.transaction;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.system.entity.domain.TransactionReceipt;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易回执单业务对象 transaction_receipt
 *
 * <AUTHOR> Li
 * @date 2023-06-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TransactionReceipt.class, reverseConvertGenerate = false)
public class TransactionReceiptBo extends NoDeptTenantEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 操作人用户编号（员工审核时记录）
     */
    @NotBlank(message = "操作人用户编号（员工审核时记录）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operatorTenantId;

    /**
     * 交易记录主键（产生交易时才会存）
     */
    @NotNull(message = "交易记录主键（产生交易时才会存）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long transactionsId;

    /**
     * 交易回执单编号（内部使用，由系统生成）
     */
    @NotBlank(message = "交易回执单编号（内部使用，由系统生成）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionReceiptNo;

    /**
     * 交易类型（Recharge-充值，Withdrawal-提现）
     */
    @NotBlank(message = "交易类型（Recharge-充值，Withdrawal-提现）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionType;

    /**
     * 交易方式
     */
    @NotBlank(message = "交易方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionMethod;

    /**
     * 交易金额
     */
    @NotNull(message = "交易金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal transactionAmount;

    /**
     * 实际交易时间
     */
    @NotNull(message = "实际交易时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date transactionTime;

    /**
     * 到账时间（员工批准提现时间）
     */
    @NotNull(message = "到账时间（员工批准提现时间）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date receiptTime;
    /**
     * 实际交易时间
     */
    @NotNull(message = "实际交易时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionTimeString;

    /**
     * 到账时间（员工批准提现时间）
     */
    @NotNull(message = "到账时间（员工批准提现时间）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiptTimeString;

    /**
     * 账户ID
     */
    @NotBlank(message = "账户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountId;

    /**
     * 账户名称
     */
    @NotBlank(message = "账户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountName;

    /**
     * 收款账户ID
     */
    @NotNull(message = "收款账户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long receiptAccountId;

    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bankName;

    /**
     * 快捷代码
     */
    @NotBlank(message = "快捷代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String swiftCode;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String note;

    /**
     * 备注（员工）
     */
    @NotBlank(message = "备注（员工）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String noteManager;

    /**
     * 第三方交易渠道单号
     */
    @NotBlank(message = "第三方交易渠道单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String thirdChannelNo;

    /**
     * 第三方交易渠道账号
     */
    @NotBlank(message = "第三方交易渠道账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String thirdChannelAccount;

    /**
     * 审核状态
     */
    @NotBlank(message = "审核状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reviewState;

    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createTimeString;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createTimeStringStart;

    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createTimeStringEnd;

    /**
     * 店铺标识
     */
    private String thirdChannelFlag;

    /**
     * 币种
     */
    private String currency;
}
