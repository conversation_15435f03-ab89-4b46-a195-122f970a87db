package com.zsmall.order.biz.factory;

import com.zsmall.common.handler.AbstractOrderOperationHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2024/01/10
 */
@SuppressWarnings("rawtypes")
@Component
public class ThirdOrderOperationFactory {
    @Resource
    private Map<String, AbstractOrderOperationHandler> strategyMap;

    public AbstractOrderOperationHandler getInvokeStrategy(String strategyName){
        return strategyMap.get(strategyName);
    }
}
