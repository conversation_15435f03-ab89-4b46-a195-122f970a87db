package com.zsmall.product.entity.domain.bo.product;

import com.zsmall.product.entity.domain.dto.product.AttributesBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 通用参数-分类属性信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-分类属性信息")
public class CategoryAttributesBody extends AttributesBody {

    @Schema(title = "attributesId")
    private Long attributesId;

    @Schema(title = "skuAttributes")
    private Boolean skuAttributes;

    @Schema(title = "requiredType")
    private String requiredType;


}
