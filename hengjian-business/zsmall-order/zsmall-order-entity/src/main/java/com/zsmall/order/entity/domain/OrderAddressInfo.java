package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.order.OrderAddressType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 主订单地址信息对象 order_address_info
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("order_address_info")
public class OrderAddressInfo extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 主订单表主键
     */
    private Long orderId;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 地址类型：收件地址、账单地址等
     */
    private OrderAddressType addressType;

    /**
     * 接收人姓名
     */
    private String recipient;

    /**
     * 国家
     */
    private String country;

    /**
     * 县
     */
    private String county;
    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 省/州
     */
    private String state;

    /**
     * 省/州代码
     */
    private String stateCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址1
     */
    private String address1;

    /**
     * 详细地址2
     */
    private String address2;
    /**
     * 详细地址3
     */
    private String address3;
    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
