package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 商品类型枚举
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
public enum ProductTypeEnum implements IEnum<String> {

    /**
     * 普通商品
     */
    NormalProduct,

    /**
     * 批发商品
     */
    WholesaleProduct,
    ;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }

}
