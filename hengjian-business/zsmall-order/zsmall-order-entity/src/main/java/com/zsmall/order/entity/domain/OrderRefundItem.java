package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 售后申请子单对象 order_refund_item
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_refund_item")
public class OrderRefundItem extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 主订单表主键
     */
    private Long orderId;

    /**
     * 子订单表主键
     */
    private Long orderItemId;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 子订单编号
     */
    private String orderItemNo;

    /**
     * 售后申请主单表主键
     */
    private Long orderRefundId;

    /**
     * 售后申请主单编号
     */
    private String orderRefundNo;

    /**
     * 售后申请子单编号
     */
    private String orderRefundItemNo;

    /**
     * 售后商品数量
     */
    private Integer refundQuantity;

    /**
     * Sku唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 参与活动类型（为空代表未参与活动）
     */
    private String activityType;

    /**
     * 参与活动编号（为空代表未参与活动）
     */
    private String activityCode;

    /**
     * 原始应付总金额（供货商）
     */
    private BigDecimal originalPayableTotalAmount;

    /**
     * 原始已预付总金额（供货商）
     */
    private BigDecimal originalPrepaidTotalAmount;

    /**
     * 原始实际支付总金额（供应商）
     */
    private BigDecimal originalActualTotalAmount;

    /**
     * 平台应付总金额（平台、分销商）
     */
    private BigDecimal platformPayableTotalAmount;

    /**
     * 平台已预付总金额（平台、分销商）
     */
    private BigDecimal platformPrepaidTotalAmount;

    /**
     * 平台实际支付总金额（平台、分销商）
     */
    private BigDecimal platformActualTotalAmount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
