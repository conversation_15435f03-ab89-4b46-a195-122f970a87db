package com.zsmall.extend.shop.service.business;


import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.extend.shop.model.vo.ConnectChannelVo;
import com.zsmall.extend.shop.shopify.model.Shop;
import com.zsmall.extend.shop.shopify.model.accesstoken.AccessToken;

/**
 * Shopify 业务回调处理接口
 */
public interface TpsBusinessShopifyService {

    /**
     * 从数据库中查询对应的AccessToken
     * @param shopDomain
     * @return
     */
    AccessToken getAccessTokenFromDb(String shopDomain);

    /**
     * 绑定授权信息
     *
     * @param shopDomain
     * @param accessToken
     * @return ConnectChannelVo 渠道链接授权情况
     */
    ConnectChannelVo bindAccount(Shop shopDomain, AccessToken accessToken) throws RStatusCodeException;

}
