package com.zsmall.common.enums.googleMap;

import lombok.Getter;

import java.util.Objects;

/**
 * 谷歌地图距离矩阵状态码
 **/
@Getter
public enum DistanceMatrixRespElementStatusEnum {

  /**
   * 请求成功
   */
  OK("Success"),
  /**
   * indicates that the origin and/or destination of this pairing could not be geocoded
   * 表示无法匹配出发地或者目的地
   */
  NOT_FOUND("Indicates that the origin and/or destination of this pairing could not be geocoded."),
  /**
   * indicates no route could be found between the origin and destination
   * 表示在始发地和目的地之间找不到路线
   */
  ZERO_RESULTS("Indicates no route could be found between the origin and destination."),
  /**
   * indicates the requested route is too long and cannot be processed
   * 表示请求的路线的距离太长，无法处理
   */
  MAX_ROUTE_LENGTH_EXCEEDED("Indicates the requested route is too long and cannot be processed."),

  /**
   * New exception types appear in the API interface. Please contact the developer for upgrade and maintenance
   * api接口出现了新的异常类型，请联系开发人员进行升级维护
   */
  EXTRA_ERROR("New exception types appear in the Google Maps API interface. Please contact the developer for upgrade and maintenance."),
  ;

  private String value;

  DistanceMatrixRespElementStatusEnum(String value) {
    this.value = value;
  }


  public static DistanceMatrixRespElementStatusEnum fromName(String name) {
    name = name.toUpperCase();
    for (DistanceMatrixRespElementStatusEnum state : DistanceMatrixRespElementStatusEnum.values()) {
      if (Objects.equals(name, state.name())) {
        return state;
      }
    }
    return null;
  }

}
