<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncFeedProductMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncFeedProduct">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="feedId" column="feed_id" jdbcType="BIGINT"/>
        <result property="productId" column="product_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,feed_id,product_id
    </sql>
</mapper>
