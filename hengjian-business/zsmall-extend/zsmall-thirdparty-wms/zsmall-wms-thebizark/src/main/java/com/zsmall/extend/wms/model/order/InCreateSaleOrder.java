package com.zsmall.extend.wms.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;


/**
 * 请求信息-创建销售发货单
 */
@Setter
@Getter
@Accessors(chain = true)
public class InCreateSaleOrder {

    /**
     * 来源单号
     */
    private String orderNo;

    /**
     * 主订单号（其他系统）
     */
    private String poNumber;

    /**
     * 渠道账户
     */
    private String channelAccount;

    /**
     * 承运商
     */
    private String carrier;

    /**
     * 快递Code
     */
    private String carrierCode;

    /**
     * 仓库code (要在cope系统中维护此code 与cope系统中仓库的对应关系 )
     */
    private String warehouseCode;

    /**
     * 订单总金额
     */
    private Double amount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 目的城市
     */
    private String shipToCity;

    /**
     * 目的州
     */
    private String shipToState;

    /**
     * 目的国家
     */
    private String shipToCountry;

    /**
     * 发运邮编
     */
    private String shipToPostal;

    /**
     * 支付时间
     */
    private String paidAt;

    /**
     * 发运联系人
     */
    private String shipToContact;

    /**
     * 发运地址1
     */
    private String shipToAddress1;

    /**
     * 发运地址2
     */
    private String shipToAddress2;
    /**
     * 发运地址3
     */
    private String shipToAddress3;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 客户邮箱
     */
    private String customerEmail;

    /**
     * 发运联系电话
     */
    private String shipToTelephone;

    /**
     * 接收人移动电话
     */
    private String shipToMobile;

    /**
     * third party 账号
     */
    private String thirdPartyAccount;

    /**
     * 'Standard-48Hours' ：标准（48小时发货）；'Same Day Ship Out':（加急当日发货）
     */
    private String shipServiceLevel;

    /**
     * 分销商自定义ID
     */
    private String distributionCustomId;

    /**
     * 分销商公司名称
     */
    private String distributionCustomName;

    /**
     * 产品行
     */
    private List<InProductItems> productItems;

    /**
     * 附件信息
     */
    private List<InAttachInfo> attachInfoItems;
}
