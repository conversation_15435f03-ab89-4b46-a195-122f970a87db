package com.zsmall.system.entity.domain.bo.extraSetting;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.TenantExtraSetting;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 租户额外设置业务对象 tenant_extra_setting
 *
 * <AUTHOR> Li
 * @date 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantExtraSetting.class, reverseConvertGenerate = false)
public class TenantExtraSettingBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 设置编码
     */
    @NotBlank(message = "设置编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String settingCode;

    /**
     * 设置内容
     */
    @NotBlank(message = "设置内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String settingValue;

    /**
     * 分类状态
     */
    @NotNull(message = "分类状态不能为空", groups = {AddGroup.class})
    private Integer state;

    /**
     * 密码
     */
//    @NotBlank(message = "密码不能为空")
    private String password;

}
