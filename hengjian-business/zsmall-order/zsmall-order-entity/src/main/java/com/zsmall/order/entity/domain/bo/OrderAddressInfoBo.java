package com.zsmall.order.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.order.entity.domain.OrderAddressInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 主订单地址信息业务对象 order_address_info
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderAddressInfo.class, reverseConvertGenerate = false)
public class OrderAddressInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 主订单表主键
     */
    @NotNull(message = "主订单表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 主订单编号
     */
    @NotBlank(message = "主订单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 地址类型：收件地址、账单地址等
     */
    @NotBlank(message = "地址类型：收件地址、账单地址等不能为空", groups = { AddGroup.class, EditGroup.class })
    private String addressType;

    /**
     * 接收人姓名
     */
    @NotBlank(message = "接收人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recipient;

    /**
     * 国家
     */
    @NotBlank(message = "国家不能为空", groups = { AddGroup.class, EditGroup.class })
    private String country;

    /**
     * 国家代码
     */
    @NotBlank(message = "国家代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String countryCode;

    /**
     * 省/州
     */
    @NotBlank(message = "省/州不能为空", groups = { AddGroup.class, EditGroup.class })
    private String state;

    /**
     * 省/州代码
     */
    @NotBlank(message = "省/州代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String stateCode;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String city;

    /**
     * 详细地址1
     */
    @NotBlank(message = "详细地址1不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address1;

    /**
     * 详细地址2
     */
    @NotBlank(message = "详细地址2不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address2;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    private String email;

    /**
     * 电话号码
     */
    @NotBlank(message = "电话号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumber;

    /**
     * 邮政编码
     */
    @NotBlank(message = "邮政编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zipCode;


}
