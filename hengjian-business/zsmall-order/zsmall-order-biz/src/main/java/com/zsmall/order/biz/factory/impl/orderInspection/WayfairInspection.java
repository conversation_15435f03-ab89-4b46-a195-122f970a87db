package com.zsmall.order.biz.factory.impl.orderInspection;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.OrderAttachmentTypeEnum;
import com.zsmall.common.enums.order.OrderExtraInfoTypeEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.support.GlobalBusinessSupport;
import com.zsmall.extend.wayfair.kit.WayfairDelegate;
import com.zsmall.extend.wayfair.kit.WayfairKit;
import com.zsmall.extend.wayfair.model.WayfairBean;
import com.zsmall.extend.wayfair.model.base.Result;
import com.zsmall.extend.wayfair.model.order.OutPurchaseOrder;
import com.zsmall.extend.wayfair.model.shipping.*;
import com.zsmall.order.biz.factory.ChannelOrderInspectionFactory;
import com.zsmall.order.biz.factory.ChannelOrderInspectionService;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.iservice.IOrderAttachmentService;
import com.zsmall.order.entity.iservice.IOrderExtraInfoService;
import com.zsmall.order.entity.iservice.IOrderItemProductSkuService;
import com.zsmall.order.entity.iservice.IOrderItemTrackingRecordService;
import com.zsmall.product.entity.domain.ProductMappingExtendWayfairWarehouse;
import com.zsmall.product.entity.iservice.IProductMappingExtendWayfairWarehouseService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Wayfair订单相关检查
 *
 * <AUTHOR>
 * @date 2023/6/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WayfairInspection implements ChannelOrderInspectionService {

    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;

    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final IOrderExtraInfoService iOrderExtraInfoService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final IWarehouseService iWarehouseService;
    private final IProductMappingExtendWayfairWarehouseService iProductMappingExtendWayfairWarehouseService;

    private final GlobalBusinessSupport globalBusinessSupport;
    private final ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;

    @Override
    public void afterPropertiesSet() throws Exception {
        ChannelOrderInspectionFactory.register(ChannelTypeEnum.Wayfair, this);
    }

    /**
     * 物流检查
     *
     * @param order
     * @param orderItemList
     * @param orderLogisticsInfo
     */
    @Override
    public void logisticsInspection(Orders order, List<OrderItem> orderItemList, OrderLogisticsInfo orderLogisticsInfo) throws RStatusCodeException {
        Map<String, String> carrierSwitchMap = globalBusinessSupport.getCarrierSwitchMap();
        Long channelId = order.getChannelId();
        TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);

        if (salesChannel == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SALES_CHANNEL_NOT_EXIST);
        }

        String orderNo = order.getOrderNo();
        String channelOrderNo = order.getChannelOrderNo();

        WayfairDelegate wayfairDelegate = WayfairKit.create(new WayfairBean(true, salesChannel.getPrivateKey(), salesChannel.getClientSecret()));

        // 查询该订单是否之前已注册过
        OrderExtraInfo WayfairRegister =
            iOrderExtraInfoService.queryByOrderNoAndInfoType(orderNo, OrderExtraInfoTypeEnum.WayfairRegister);
        log.info("Wayfair订单{}物流检查 extraInfo = {}", orderNo, JSONUtil.toJsonStr(WayfairRegister));

        // Wayfair订单原始报文
        OrderExtraInfo OrderOriginBody =
            iOrderExtraInfoService.queryByOrderNoAndInfoType(orderNo, OrderExtraInfoTypeEnum.OrderOriginBody);
        log.info("Wayfair订单{}物流检查 orderOriginBody == {}", orderNo, JSONUtil.toJsonStr(OrderOriginBody));

        try {
            if (WayfairRegister == null) {
                // 注册wayfair订单获取物流单号
                InRegistrationInput inRegistrationInput = new InRegistrationInput();
                RegistrationInput registrationInput = new RegistrationInput();
                registrationInput.setPoNumber(channelOrderNo);
                inRegistrationInput.setRegistrationInput(registrationInput);
                Result<OutLabelGenerationPurchaseOrders<OutLabelGenerationRegister>> outLabelGenerationEvent =
                    wayfairDelegate.shippingApi().registerShipping(inRegistrationInput);
                OutLabelGenerationPurchaseOrders<OutLabelGenerationRegister> data = outLabelGenerationEvent.getData();
                OutLabelGenerationRegister purchaseOrders = data.getPurchaseOrders();
                log.info("Wayfair订单{}物流检查 purchaseOrders = {}", orderNo, JSONUtil.toJsonStr(purchaseOrders));

                if (purchaseOrders == null) {
                    throw new RStatusCodeException(OrderStatusCodeEnum.WAYFAIR_REGISTER_SHIPPING_FAILED);
                }

                OutLabelGenerationEvent register = purchaseOrders.getRegister();
                List<ShippingLabelInterface> shippingLabelInfos = register.getShippingLabelInfo();
                OutPurchaseOrder purchaseOrder = register.getPurchaseOrder();

                if (CollUtil.isEmpty(shippingLabelInfos) || purchaseOrder == null || purchaseOrder.getShippingInfo() == null) {
                    throw new RStatusCodeException(OrderStatusCodeEnum.WAYFAIR_REGISTER_SHIPPING_FAILED);
                }

                // 转换Wayfair传来的承运商
                PurchaseOrderShipping shippingInfo = purchaseOrder.getShippingInfo();
                String carrier = shippingInfo.getCarrierCode();
                if (!StringUtils.equalsAny(carrier, "FedEx", "UPS") && StringUtils.isNotBlank(carrier)) {
                    String mapCarrier = carrierSwitchMap.get(carrier);
                    if (StringUtils.isNotBlank(mapCarrier)) {
                        carrier = mapCarrier;
                    }
                }

                String newCarrier = "";
                // 物流单号的匹配处理不能影响ShippingLabel的获取
                try {
                    int shippingLabelInfoSize = CollUtil.size(shippingLabelInfos);
                    List<OrderItemTrackingRecord> trackingList = new ArrayList<>();
                    // 需要删除的旧跟踪单信息
                    List<String> oldTrackingRecord = new ArrayList<>();

                    Integer listIndex = 0;
                    for (OrderItem orderItem : orderItemList) {
                        Integer totalQuantity = orderItem.getTotalQuantity();
                        String orderItemNo = orderItem.getOrderItemNo();

                        if (CollUtil.size(orderItemList) == 1) {
                            for (ShippingLabelInterface shippingLabelInfo : shippingLabelInfos) {
                                // 给子订单设置跟踪单数据，并接收新的承运商
                                newCarrier = setOrderItemTracking(orderNo, orderItem, carrier, shippingLabelInfo, trackingList);
                            }
                        } else {
                            log.info("Wayfair订单{}物流检查 子订单编号 = {} Wayfair回传的物流数据数量 = {} 当前遍历下标 = {} 子订单总数量 = {}",
                                orderItem.getOrderItemNo(), shippingLabelInfoSize, listIndex, totalQuantity);
                            for (Integer item = 0; item < totalQuantity; item++) {
                                Integer nowIndex = listIndex + item;
                                // 根据子订单数量不断获取Shipping的信息
                                if (nowIndex < shippingLabelInfoSize) {
                                    ShippingLabelInterface shippingLabelInfo = shippingLabelInfos.get(nowIndex);
                                    newCarrier = setOrderItemTracking(orderNo, orderItem, carrier, shippingLabelInfo, trackingList);
                                } else {  // 如果子订单下标数量已经超过shippingLabelInfos的size，则每次都取最后一个
                                    ShippingLabelInterface shippingLabelInfo = shippingLabelInfos.get(shippingLabelInfoSize - 1);
                                    newCarrier = setOrderItemTracking(orderNo, orderItem, carrier, shippingLabelInfo, trackingList);
                                }
                            }
                            listIndex += totalQuantity;
                        }
                        // 删除旧的跟踪单数据
                        oldTrackingRecord.add(orderItemNo);
                    }


                    if (CollUtil.isNotEmpty(oldTrackingRecord)) {
                        iOrderItemTrackingRecordService.trackingListDisassociate(oldTrackingRecord);
                    }

                    if (CollUtil.isNotEmpty(trackingList)) {
                        iOrderItemTrackingRecordService.saveOrUpdateBatch(trackingList);
                    }
                } catch (Exception e) {
                    log.error("Wayfair订单{}物流检查 - 物流单号匹配出现错误 原因 {}", orderNo, e.getMessage(), e);
                    throw e;
                }

                downloadWayfairShippingLabel(wayfairDelegate, orderNo, channelOrderNo);
                orderLogisticsInfo.setShippingLabelExist(true);

                log.info("getWayFairShipInfo complete, save OrderExtraInfo");
                // 记录Wayfair订单注册信息
                OrderExtraInfo orderExtraInfo = new OrderExtraInfo();
                orderExtraInfo.setOrderNo(orderNo);
                orderExtraInfo.setInfoType(OrderExtraInfoTypeEnum.WayfairRegister);
                orderExtraInfo.setEntityName(OutLabelGenerationRegister.class.getName());
                orderExtraInfo.setContent(JSONUtil.parseObj(purchaseOrders));
                iOrderExtraInfoService.save(orderExtraInfo);

                // 新承运商不为空，并且与原承运商不同，说明原承运商是错误的，需要覆盖
                if (StrUtil.isNotBlank(newCarrier) && !StrUtil.equals(newCarrier, carrier)) {
                    // 覆盖原承运商，之后的通知发货才会取用到正确的
                    OutPurchaseOrder outPurchaseOrder = JSONUtil.toBean(OrderOriginBody.getContent(), OutPurchaseOrder.class);
                    PurchaseOrderShipping originShippingInfo = outPurchaseOrder.getShippingInfo();
                    originShippingInfo.setCarrierCode(newCarrier);
                    OrderOriginBody.setContent(JSONUtil.parseObj(purchaseOrders));
                    iOrderExtraInfoService.saveOrUpdate(OrderOriginBody);
                    orderLogisticsInfo.setLogisticsCompanyName(newCarrier);
                }
            }

            for (OrderItem orderItem : orderItemList) {
                Integer totalQuantity = orderItem.getTotalQuantity();
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemIdNoTenant(orderItem.getId());
                // Wayfair匹配仓库
                String productSkuCode = orderItemProductSku.getProductSkuCode();
                String channelWarehouseCode = orderItemProductSku.getChannelWarehouseCode();
                List<ProductMappingExtendWayfairWarehouse> wayfairWarehouseList = iProductMappingExtendWayfairWarehouseService.queryByThirdWarehouseId(channelWarehouseCode, productSkuCode, totalQuantity, channelId);
                if (CollUtil.isNotEmpty(wayfairWarehouseList)) {
                    ProductMappingExtendWayfairWarehouse wayfairWarehouse = wayfairWarehouseList.get(RandomUtil.randomInt(0, CollUtil.size(wayfairWarehouseList)));
                    String warehouseSystemCode = wayfairWarehouse.getWarehouseSystemCode();
                    orderItemProductSku.setSpecifyWarehouse(warehouseSystemCode);
                    iOrderItemProductSkuService.updateById(orderItemProductSku);
                }
            }
        } catch (RStatusCodeException e) {
            log.error("Wayfair订单{}物流检查发生业务错误，原因 = {}", orderNo, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Wayfair订单{}物流检查发生未知错误，原因 = {}", orderNo, e.getMessage(), e);
            throw new RStatusCodeException(OrderStatusCodeEnum.WAYFAIR_REGISTER_SHIPPING_ENCOUNTERED_ERROR.args(e.getMessage()));
        }
    }

    /**
     * 设置子订单物流跟踪单信息
     * @param orderNo
     * @param orderItem
     * @param carrier
     * @param shippingLabelInfo
     * @param trackingList
     * @return
     */
    private String setOrderItemTracking(String orderNo, OrderItem orderItem, String carrier, ShippingLabelInterface shippingLabelInfo,
                                        List<OrderItemTrackingRecord> trackingList) {

        OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemIdNoTenant(orderItem.getId());
        // 最终供货商
        String newCarrier = carrier;
        String trackingNumber = shippingLabelInfo.getTrackingNumber();
        log.info("carrier = {}, StrUtil.length(trackingNumber) = {}", carrier, StrUtil.length(trackingNumber));
        // 2022-7-18新需求，根据运单号长度判断承运商，12位数的是FedEx，18位的是UPS，两种都不符合就保持原有
        if (StrUtil.length(trackingNumber) == 12) {
            newCarrier = "FedEx";
        } else if (StrUtil.length(trackingNumber) == 18) {
            newCarrier = "UPS";
        }
        log.info("newCarrier = {}", newCarrier);

        OrderItemTrackingRecord tracking = new OrderItemTrackingRecord();
        tracking.setOrderNo(orderNo);
        tracking.setOrderItemNo(orderItem.getOrderItemNo());
        tracking.setSku(orderItemProductSku.getSku());
        tracking.setProductSkuCode(orderItem.getProductSkuCode());
        tracking.setLogisticsCarrier(newCarrier);
        tracking.setLogisticsTrackingNo(trackingNumber);
        tracking.setQuantity(orderItem.getTotalQuantity());
        trackingList.add(tracking);
        return newCarrier;
    }

    /**
     * 下载快递标签
     * @param wayfairDelegate
     * @param orderNo
     * @param channelOrderNo
     * @throws Exception
     */
    public void downloadWayfairShippingLabel(WayfairDelegate wayfairDelegate, String orderNo, String channelOrderNo)
        throws Exception {
        // 获取快递标签
        @Cleanup InputStream inputStream = wayfairDelegate.shippingApi().downloadShippingLabel(channelOrderNo);
        String fileName = orderNo + ".pdf";
        OSSUploadEvent ossUploadEvent = new OSSUploadEvent(inputStream, fileName);
        SpringUtils.context().publishEvent(ossUploadEvent);
        SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();
        String originalName = sysOssVo.getOriginalName();
        String suffix = FileUtil.getSuffix(originalName);

        OrderAttachment newOrderAttachment = new OrderAttachment();
        newOrderAttachment.setOssId(sysOssVo.getOssId());
        newOrderAttachment.setOrderNo(orderNo);
        newOrderAttachment.setAttachmentName(sysOssVo.getFileName());
        newOrderAttachment.setAttachmentOriginalName(originalName);
        newOrderAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
        newOrderAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
        newOrderAttachment.setAttachmentSuffix(suffix.toLowerCase());
        newOrderAttachment.setAttachmentType(OrderAttachmentTypeEnum.ShippingLabel);

        iOrderAttachmentService.deleteByOrderNoAndAttachmentType(orderNo, OrderAttachmentTypeEnum.ShippingLabel);
        iOrderAttachmentService.save(newOrderAttachment);
    }

}
