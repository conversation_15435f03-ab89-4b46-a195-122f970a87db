package com.zsmall.system.entity.domain.bo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求信息-订单导入审核
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "请求信息-导入记录")
public class ReqImportRecordV2Body {

    @Schema(title = "临时表单号")
    private String tempOrderNo;

    @Schema(title = "订单编号")
    private String orderNo;

    @Schema(title = "记录编号")
    private String recordNo;

}
