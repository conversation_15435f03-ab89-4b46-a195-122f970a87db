package com.zsmall.warehouse.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.system.mapper.SysConfigMapper;
import com.hengjian.system.service.ISysConfigService;
import com.zsmall.extend.wms.kit.ThebizarkDelegate;
import com.zsmall.extend.wms.kit.ThebizarkKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.extend.wms.model.base.Result;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.domain.dto.WarehouseInventoryDTO;
import com.zsmall.warehouse.entity.domain.req.WarehouseInventoryReq;
import com.zsmall.warehouse.entity.domain.resp.WarehouseInventoryResp;
import com.zsmall.warehouse.entity.domain.vo.WarehouseInventoryVo;
import com.zsmall.warehouse.entity.iservice.IWarehouseBizArkConfigService;
import com.zsmall.warehouse.service.WarehouseInventoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/29 14:42
 */
@Slf4j
@Service
public class WarehouseInventoryServiceImpl implements WarehouseInventoryService {
    @Resource
    private IWarehouseBizArkConfigService iWarehouseBizArkConfigService;
    @Resource
    private IProductSkuService iProductSKuService;
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private ISysConfigService iSysConfigService;

    private ThebizarkDelegate initDelegate(String supplierId) {
        WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierId);
        if (ObjectUtil.isEmpty(bizArkConfig)) {
            log.error("未找到供应商{}的配置信息", supplierId);
            return null;
        }
        return ThebizarkKit.create(new ThebizarkBean(bizArkConfig.getBizArkApiUrl(), bizArkConfig.getBizArkSecretKey(), bizArkConfig.getBizArkChannelAccount()));
    }
    @Override
    public R<WarehouseInventoryVo> getInventoryPlan(WarehouseInventoryDTO dto) throws Exception {
        String itemNo = dto.getItemNo();
        ProductSku productSku = iProductSKuService.queryByProductSkuCode(itemNo);
        String sku = productSku.getSku();
        String supplierTenantId = productSku.getTenantId();
        WarehouseInventoryVo warehouseInventoryVo = new WarehouseInventoryVo();
        // 库存查询-sku的所有仓库+总到货数量
        WarehouseInventoryReq inventoryReq = new WarehouseInventoryReq();
        inventoryReq.setQuerySkus(Collections.singletonList(sku));
        inventoryReq.setQueryType("1");
        ThebizarkDelegate delegate = initDelegate(supplierTenantId);
        String reqJson = JSONUtil.toJsonStr(inventoryReq);
        if (delegate != null) {
            Result<JSONArray> inventoryPlanResult = null;
            try{
               inventoryPlanResult = delegate.warehousingApi().getInventoryPlan(reqJson);
            }catch (Exception e){
                throw new Exception("获取库存计划异常,请联系管理员");
            }
            JSONArray datas = inventoryPlanResult.getData();
            if(ObjectUtil.isEmpty(datas)){
                throw new RuntimeException("获取库存计划异常,请联系管理员");
            }
            List<WarehouseInventoryResp> results = JSON.parseArray(datas.toJSONString(0), WarehouseInventoryResp.class);

            // JSONArray datas 转换为 List<WarehouseInventoryResp>
            Integer wk2Num = 0;
            for (WarehouseInventoryResp data : results) {
                wk2Num= wk2Num+data.getEstimatedArrivalQuantityWk2();
            }

            String wk2Quantity=null;
            if(wk2Num>999999){
                wk2Quantity = "999999+";
            }else {
                wk2Quantity = wk2Num.toString();
            }
            warehouseInventoryVo.setEstimatedQuantity(wk2Quantity);
            String weekDate = getWeekDateNotDescription(2);
            warehouseInventoryVo.setEstimatedArrivalTime(weekDate);

        }
        return R.ok(warehouseInventoryVo);
    }

    @Override
    @InMethodLog("库存计划导出")
    public void inventoryPlanExport(WarehouseInventoryDTO dto, HttpServletResponse response) {
        String itemNo = dto.getItemNo();
        ProductSku productSku = iProductSKuService.queryByProductSkuCode(itemNo);
        String sku = productSku.getSku();
        String supplierTenantId = productSku.getTenantId();
        // 库存查询-sku的所有仓库+总到货数量
        WarehouseInventoryReq inventoryReq = new WarehouseInventoryReq();
        inventoryReq.setQuerySkus(Collections.singletonList(sku));
        inventoryReq.setQueryType("0");
        ThebizarkDelegate delegate = initDelegate(supplierTenantId);
        String reqJson = JSONUtil.toJsonStr(inventoryReq);
        response.setHeader("Content-disposition", "attachment;filename=inventory_plan.xlsx");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        try {
            delegate.warehousingApi().exportBySku(reqJson,response);
        }catch (Exception e){
            log.error("erp库存计划导出异常",e);
        }

    }

    @Override
    public R<Boolean> isVisibleDistributorConfiguration() {
        String tenantType = LoginHelper.getTenantType();
        String tenantId = LoginHelper.getTenantId();
        if(!TenantType.Distributor.name().equals(tenantType)){
            return R.ok(Boolean.FALSE);
        }else {
            // 分销商要看是不是在配置内
            boolean contentExist = iSysConfigService.isContentExist(tenantId,"is_visible_distributor_configuration");
            return R.ok(contentExist);
        }
    }

    @Override
    public R<Boolean> isVisibleSupplierConfiguration() {
        String tenantType = LoginHelper.getTenantType();
        String tenantId = LoginHelper.getTenantId();
        if(TenantType.Distributor.name().equals(tenantType)){
            return R.ok(Boolean.FALSE);
        }else {
            // 供应商要看是不是在配置内
            boolean contentExist = iSysConfigService.isContentExist(tenantId,"is_visible_supplier_configuration");
            return R.ok(contentExist);
        }
    }

    /**
     * 获取指定周表头
     *
     * @param weekSign 从起始值0开始
     * @return 示例 WK6周到货量2.19-2.25
     */
    public static String getWeekDate(Integer weekSign) {
        LocalDate localDate = LocalDate.now().plusWeeks(weekSign);
        LocalDate nextWeek = localDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate nextWeekEnd = localDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        ++weekSign;
        return "WK" + weekSign + "预计到货量" + System.getProperty("line.separator") + nextWeek.getMonthValue() + "." + nextWeek.getDayOfMonth() + "-" + nextWeekEnd.getMonthValue() + "." + nextWeekEnd.getDayOfMonth();
    }

    public static String getWeekDateNotDescription(Integer weekSign) {
        LocalDate localDate = LocalDate.now().plusWeeks(weekSign);
        int year = LocalDate.now().getYear();
        LocalDate nextWeek = localDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate nextWeekEnd = localDate.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        ++weekSign;
        return year+"."+nextWeek.getMonthValue() + "." + nextWeek.getDayOfMonth() + "-" + year+"."+nextWeekEnd.getMonthValue() + "." + nextWeekEnd.getDayOfMonth();
    }

}
