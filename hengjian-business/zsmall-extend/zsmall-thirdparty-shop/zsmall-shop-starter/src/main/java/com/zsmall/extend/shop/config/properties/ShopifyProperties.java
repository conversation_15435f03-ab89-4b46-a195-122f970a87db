package com.zsmall.extend.shop.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * shopify 配置
 */
@Data
@ConfigurationProperties(prefix = "thirdparty-shop.shopify")
public class ShopifyProperties {
    /**
     * 过滤开关
     */
    private Boolean enabled;

    /**
     * 授权URL
     */
    private String authorizeUrl;

    /**
     * key
     */
    private String clientKey;

    /**
     * 安全码
     */
    private String clientSecret;

    /**
     * 授权范围
     */
    private String scope;


    private String grantOptions;

    /**
     * 重定向地址
     */
    private String redirectUri;

    /**
     * 接口版本
     */
    private String apiVersion;

    /**
     * 删除店铺接口地址
     */
    private String deleteUrl;

    /**
     * 默认创建的履约服务名字
     */
    private String fulfillmentServiceName;

    /**
     * 默认创建的履约服务回调地址
     */
    private String fulfillmentServiceCallback;

}
