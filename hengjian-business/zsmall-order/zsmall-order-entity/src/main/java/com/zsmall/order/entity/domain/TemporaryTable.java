package com.zsmall.order.entity.domain;

import lombok.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 临时对象 temporary_table
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemporaryTable implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long value1;

    /**
     *
     */
    private Long value2;

    /**
     *
     */
    private String value3;

    /**
     *
     */
    private String value4;

    public TemporaryTable(Long value1) {
        this.value1 = value1;
    }
}
