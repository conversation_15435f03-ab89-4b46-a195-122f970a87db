package com.zsmall.extend.wms.model.product;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * 产品
 */
@Setter
@Getter
@Accessors(chain = true)
public class ProductItems {

    /**
     * sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 退货数量
     */
    private String qty;

    /**
     * 销售金额
     */
    private Double skuAmount;

    /**
     * 投保金额
     */
    private Double insureAmount;

}
