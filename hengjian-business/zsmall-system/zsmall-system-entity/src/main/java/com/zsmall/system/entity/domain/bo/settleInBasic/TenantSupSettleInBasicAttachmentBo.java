package com.zsmall.system.entity.domain.bo.settleInBasic;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.TenantSupSettleInBasicAttachment;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 供应商入驻基本信息附件业务对象 tenant_sup_settle_in_basic_attachment
 *
 * <AUTHOR> Li
 * @date 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantSupSettleInBasicAttachment.class, reverseConvertGenerate = false)
public class TenantSupSettleInBasicAttachmentBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 附件名字
     */
    @NotBlank(message = "附件名字不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 保存地址
     */
    @NotBlank(message = "保存地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String savePath;

    /**
     * 展示地址
     */
    @NotBlank(message = "展示地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String showUrl;

    /**
     * 文件类型
     */
    @NotNull(message = "文件类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long type;

    /**
     * 文件性质（证件、营业执照等）
     */
    @NotNull(message = "文件性质（证件、营业执照等）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long fileNature;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sort;

    /**
     * 供应商入驻基础信息id
     */
    @NotNull(message = "供应商入驻基础信息id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long basicId;


}
