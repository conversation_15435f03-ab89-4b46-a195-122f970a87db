package com.zsmall.extend.shop.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.exception.ServiceException;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.extend.shop.amazon.bean.in.InAuthorization;
import com.zsmall.extend.shop.amazon.bean.out.OutAccessToken;
import com.zsmall.extend.shop.amazon.business.entity.domain.AmazonAppConfig;
import com.zsmall.extend.shop.amazon.business.entity.iservice.IAmazonAppConfigService;
import com.zsmall.extend.shop.config.properties.AmazonProperties;
import com.zsmall.extend.shop.enums.DispatchType;
import com.zsmall.extend.shop.model.vo.AmznAccessToken;
import com.zsmall.extend.shop.service.TpsAmazonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;

/**
 * Amazon接口实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(value = "thirdparty-shop.amazon.enabled", havingValue = "true")
public class TpsAmazonServiceImpl implements TpsAmazonService {

    private final AmazonProperties amazonProperties;
    private final IAmazonAppConfigService amazonAppConfigService;
    private final long EXPIRE_AUTHORIZE_CODE = 2 * 60 * 3600;
    private final String KEY_WEBSITEAUTHORIZEURL = "websiteAuthorizeUrl";
    private final String KEY_AUTHORIZEPARAMS = "authorizeParams";
    private final String KEY_CLIENTID = "clientId";
    private final String KEY_CLIENTSECRET = "clientSecret";


    /**
     * 获取应用重定向地址
     *
     * @param appConfigId
     * @return
     */
    @Override
    public String getAmazonAppRedirectUrl(String appConfigId) {
        String appRedirectUrl = amazonProperties.getAppRedirectUrl();

        return appRedirectUrl + "/" + appConfigId;
    }

    @Override
    public String getWebsiteAuthorizeUrl(String appBizId) {
        // appBizId 表主键
        // 暂不考虑加缓存，毕竟查询次数较少
        AmazonAppConfig amazonAppConfig = amazonAppConfigService.selectConfigById(appBizId);
        String websiteAuthorizeUrl = getAuthorizeValue(amazonAppConfig, KEY_WEBSITEAUTHORIZEURL);
        String appId = amazonAppConfig.getAppId();

        String state = UUID.randomUUID(true).toString(true);
        websiteAuthorizeUrl = websiteAuthorizeUrl.replace("{applicationId}", appId).replace("{state}", state);
        log.info("websiteAuthorizeUrl = {}", websiteAuthorizeUrl);
        this.addAuthorizeState2Redis(state, appBizId);
        return websiteAuthorizeUrl;
    }

    private static String getAuthorizeValue(AmazonAppConfig amazonAppConfig, String key) {
        String authorizeInfo = amazonAppConfig.getAuthorizeInfo();
        if(StrUtil.isBlank(authorizeInfo) || !JSONUtil.isTypeJSON(authorizeInfo)) {
            throw new ServiceException("Amazon app configuration invalid.");
        }
        JSONObject authorizeObject = JSONUtil.parseObj(authorizeInfo);
        return authorizeObject.getStr(key);
    }

    private static String getEndpointValue(AmazonAppConfig amazonAppConfig, String key) {
        String endpoint = amazonAppConfig.getEndpoint();
        if(StrUtil.isBlank(endpoint) || !JSONUtil.isTypeJSON(endpoint)) {
            throw new ServiceException("Amazon app configuration invalid.");
        }
        JSONObject endpointObject = JSONUtil.parseObj(endpoint);
        return endpointObject.getStr(key);
    }

    @Override
    public boolean checkAuthorizeState(String state) {
        String key = getAuthorizeStateKey(state);
        return RedisUtils.hasKey(key);
    }

    @Override
    public void addAuthorizeState2Redis(String state, String appBizId) {
        String key = this.getAuthorizeStateKey(state);
        log.info("key = {}, value = {}", key, state);
        RedisUtils.setCacheObject(key, appBizId, Duration.ofSeconds(EXPIRE_AUTHORIZE_CODE));
    }

    /**
     * 构造回调地址
     *
     * @param map
     * @return
     */
    @Override
    public String buildCallbackUrl(Map<String, String> map) {
        String state = map.get("state");
        String amazonCallbackUri = map.get("amazon_callback_uri");

        String appBizId = getBizId(state);
        // 暂不考虑加缓存，毕竟查询次数较少
        AmazonAppConfig amazonAppConfig = amazonAppConfigService.selectConfigById(appBizId);
        String appAuthorizeParams = getAuthorizeValue(amazonAppConfig, KEY_AUTHORIZEPARAMS);


        state = UUID.randomUUID(true).toString(true);
        String appAuthorizeUrl = amazonProperties.getAuthorizeRedirectUrl();

        appAuthorizeParams = appAuthorizeParams.replace("{redirectUri}", URLEncodeUtil.encode(appAuthorizeUrl, StandardCharsets.UTF_8))
                                               .replace("{state}", state);

        StringBuilder redirectUrlBuffer = new StringBuilder(amazonCallbackUri).append(appAuthorizeParams);
        log.info("redirectUrl => {}", redirectUrlBuffer);

        this.addAuthorizeState2Redis(state, appBizId);
        return redirectUrlBuffer.toString();
    }

    /**
     * 根据state获取业务表上数据值
     * @param state
     * @return
     */
    private String getBizId(String state) {
        String appBizId;
        String authorizeStateKey = getAuthorizeStateKey(state);
        try {
            appBizId = RedisUtils.getCacheObject(authorizeStateKey);
        } finally {
            RedisUtils.deleteObject(authorizeStateKey);
        }

        return appBizId;
    }

    /**
     * 构造网站URL
     *
     * @param map
     * @return
     */
    @Override
    public String buildWebsiteUrl(Map<String, String> map) {
        String state = map.get("state");
        String appBizId = getBizId(state);
        map.put("appBizId", appBizId);

        String authorizeState = UUID.randomUUID(true).toString(true);
        String key = this.getAuthorizeStateKey(authorizeState);
        String valueString = JSONUtil.toJsonStr(map);
        log.info("key = {}, value = {}", key, valueString);
        RedisUtils.setCacheObject(key, valueString, Duration.ofSeconds(EXPIRE_AUTHORIZE_CODE));

        String websiteLoginUrl = amazonProperties.getWebsiteLoginUrl();

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("dispatchType", DispatchType.AMAZON_AUTHORIZE);
        jsonObject.set("state", authorizeState);
        String params = Base64.encode(jsonObject.toString().getBytes());
        log.info("index websiteLoginUrl = {}, state: {}", websiteLoginUrl, params);
        return String.format(websiteLoginUrl, params);
    }

    private String getAuthorizeStateKey(String state) {
        return GlobalConstants.GLOBAL_REDIS_KEY + "amazon:" + state;
    }


    /**
     * 根据授权码获取授权token信息
     * @param appBizId
     * @param code
     * @return
     */
    public AmznAccessToken getToken(String appBizId, String code) throws IOException {
        AmznAccessToken amznAccessToken = null;
        AmazonAppConfig amazonAppConfig = amazonAppConfigService.selectConfigById(appBizId);
        String clientId = getAuthorizeValue(amazonAppConfig, KEY_CLIENTID);
        String clientSecret = getAuthorizeValue(amazonAppConfig, KEY_CLIENTSECRET);
        String appId = amazonAppConfig.getAppId();

        InAuthorization inAuthorization = new InAuthorization(code);
        JSONObject params = JSONUtil.parseObj(inAuthorization);
        params.putOnce("client_id", clientId);
        params.putOnce("client_secret", clientSecret);

        String url = getEndpointValue(amazonAppConfig, "token");
        OutAccessToken outAccessToken = null;
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        String requestBody = params.toString();
        log.info("request json = {}", requestBody);
        Request request = new Request.Builder()
            .url(url)
            .post(RequestBody.create(mediaType, requestBody))
            .build();

        OkHttpClient okHttpClient = new OkHttpClient();
        Response response = okHttpClient.newCall(request).execute();
        if (response.isSuccessful()) {
            String responseString =  response.body().string();
            log.info("responseString = {}", responseString);
            outAccessToken = JSONUtil.toBean(responseString, OutAccessToken.class);
            // 设置应用Id
            if(outAccessToken != null) {
                amznAccessToken = new AmznAccessToken(outAccessToken.getRefreshToken(), appId);
//                outAccessToken.setAppId(appId);
            }
        } else {
            throw new IOException("Unexpected code = " + response.code() + ", message = " + response.message());
        }

        return amznAccessToken;
    }
}
