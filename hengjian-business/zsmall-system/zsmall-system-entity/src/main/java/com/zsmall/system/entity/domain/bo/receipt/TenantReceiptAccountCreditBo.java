package com.zsmall.system.entity.domain.bo.receipt;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.system.entity.domain.TenantReceiptAccountCredit;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 收款账户信用卡信息业务对象 tenant_receipt_account_credit
 *
 * <AUTHOR> Li
 * @date 2023-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantReceiptAccountCredit.class, reverseConvertGenerate = false)
public class TenantReceiptAccountCreditBo extends NoDeptBaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 收款账户主表id
     */
    @NotNull(message = "收款账户主表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long receiptAccountId;

    /**
     * 开户行国别/地区（国家id）
     */
    @NotNull(message = "开户行国别/地区（国家id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long countryId;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountNumber;

    /**
     * 开户行名称
     */
    @NotBlank(message = "开户行名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bankName;

    /**
     * SWIFT Code
     */
    @NotBlank(message = "SWIFT Code不能为空", groups = { AddGroup.class, EditGroup.class })
    private String swiftCode;

    /**
     * 账户名称
     */
    @NotBlank(message = "账户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountName;

    /**
     * 币种
     */
    @NotBlank(message = "币种不能为空", groups = { AddGroup.class, EditGroup.class })
    private String currency;

    /**
     * 开户行地址
     */
    @NotBlank(message = "开户行地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bankAddress;

    /**
     * 账号持有人地址
     */
    @NotBlank(message = "账号持有人地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String holderAddress;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String note;


}
