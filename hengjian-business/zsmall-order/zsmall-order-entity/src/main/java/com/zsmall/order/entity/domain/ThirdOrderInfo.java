package com.zsmall.order.entity.domain;

import com.hengjian.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 第三方订单信息主对象 third_order_info
 *
 * <AUTHOR> Li
 * @date 2024-08-05
 */
@Data
@TableName("third_order_info")
@Accessors(chain = true)
public class ThirdOrderInfo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    private String tenantId;

    /**
     * 店铺名称
     */
    private String accountId;

    /**
     * 第三方订单号
     */
    private String orderNum;

    /**
     * 订单总金额
     */
    private BigDecimal amount;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 发货状态
     */
    private String shippingStatus;

    /**
     * 买家名称
     */
    private String buyName;

    /**
     * 买家email
     */
    private String buyEmail;

    /**
     * display_seller
     */
    private String displaySeller;

    /**
     * 订单发货类型  MFN  AFM
     */
    private String fulfillmentChannel;

    /**
     * 发货等级
     */
    private String shipServiceLevel;

    /**
     * 收件人
     */
    private String shippingName;

    /**
     * 地址1
     */
    private String addressline1;

    /**
     * 地址2
     */
    private String addressline2;

    /**
     * 城市
     */
    private String city;

    /**
     * 省/州代码
     */
    private String stateOrRegion;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家code
     */
    private String countryCode;

    /**
     * 邮编
     */
    private String postalCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 创建时间
     */
    private String created;

    /**
     * 付款时间
     */
    private String paymentDate;

    /**
     * 更新时间
     */
    private String updated;

    /**
     * 最晚发货时间
     */
    private String latestShipDate;

    /**
     * 最早发货时间
     */
    private String earliestShipDate;

    /**
     * 是否商业订单
     */
    private String isBusinessOrder;

    /**
     * 是否为prime的订单
     */
    private String isPrime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    // 2025-02-12 新增字段 amazonVC
    private String sales_channel;
    private String purchase_order_state;
    private String customer_refer_no;
    private String ship_to_party;
    private String fob_shipment_payment_method;
    private String address_code;
    // 2025-02-12 新增字段 amazonSC
    private String display_ordernum;
    // 2025-02-13 新增字段 temu
    private String platform_cancel_time;
}
