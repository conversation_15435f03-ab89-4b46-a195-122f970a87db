package com.zsmall.system.entity.domain.bo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求参数-渠道通用
 *
 * <AUTHOR>
 * @date 2021/7/6 16:27
 */
@Data
@NoArgsConstructor
@Schema(name = "请求参数-渠道通用")
public class ReqDisconnectAppBody {

    @Schema(title = "渠道id")
    private Long channelId;

    @Schema(title = "渠道")
    private String channel;

}
