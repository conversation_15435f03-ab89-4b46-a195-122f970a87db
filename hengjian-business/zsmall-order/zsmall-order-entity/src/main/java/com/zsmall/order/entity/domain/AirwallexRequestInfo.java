package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Access;
import javax.persistence.AccessType;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Getter
@Setter
@Accessors(chain = true)
@Access(AccessType.FIELD)
@JsonInclude(JsonInclude.Include.USE_DEFAULTS)
@AllArgsConstructor
@NoArgsConstructor
@TableName("airwallex_request_info")
public class AirwallexRequestInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Long id;

    /**
     * 空中云汇请求接口 requestId
     */
    private String requestId;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     *  支付类型：1：空中云汇收单 2：派安盈收单 3：连连收单 4：派安盈订单支付
     */
    private Integer payType;

    /**
     * 类型 1：支付 2：充值
     */
    private Integer type;

    /**
     * 使用逗号分隔开的订单号
     */
    private String orderNo;

    /**
     * 接口返回信息
     */
    private String returnInfo;

    /**
     * 异常信息
     */
    private String exceptionMessage;

    /**
     * 是否处理成功标识 1：成功 2：失败
     */
    private Integer isSuccess;

    /**
     * AES加密key
     */
    private String aesKey;

    /**
     * 派安盈请求支付参数
     */
    private String commitId;


    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
