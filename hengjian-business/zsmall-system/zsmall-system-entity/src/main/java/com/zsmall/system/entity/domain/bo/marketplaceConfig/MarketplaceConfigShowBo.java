package com.zsmall.system.entity.domain.bo.marketplaceConfig;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 请求体-展示商城配置
 *
 * <AUTHOR>
 * @date 2023/9/6
 */
@Data
public class MarketplaceConfigShowBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置使用的页面
     */
    private String applicablePage;
    /**
     * 站点
     */
    private String site;

    /**
     * 模块类型
     */
    private List<String> moduleTypeList;

}
