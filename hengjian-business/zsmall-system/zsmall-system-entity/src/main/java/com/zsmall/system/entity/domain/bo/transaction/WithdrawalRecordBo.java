package com.zsmall.system.entity.domain.bo.transaction;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> @date 2023年7月6日
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Data
public class WithdrawalRecordBo {

    /**
     * 收款账户code
     */
    private String accountCode;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 账户类型（Credit-信用卡，Payoneer-P卡）
     */
    private String accountType;

    /**
     * 提现金额
     */
    private BigDecimal receiptAmount;

    /**
     * 备注
     */
    private String note;

    /**
     * 账单编号
     */
    private List<String> billNoList;

}
