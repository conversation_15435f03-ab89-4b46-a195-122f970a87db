package com.zsmall.product.entity.domain.bo.product;

import lombok.Data;

import java.util.List;

/**
 * 请求参数-商品价格信息
 */
@Data
public class ProductPriceBo {

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 查询内容
     */
    private String queryValue;

    /**
     * SKU的上架状态 OnShelf-上架；OffShelf-下架
     */
    private String skuShelfState;

    /**
     * 审计状态
     */
    private String skuAuditStatus;

    private List<String> itemNos;

    private Long siteId;

}
