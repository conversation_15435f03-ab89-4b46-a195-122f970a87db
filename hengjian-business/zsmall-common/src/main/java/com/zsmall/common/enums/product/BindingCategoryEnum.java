package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 全局属性绑定分类枚举
 *
 * <AUTHOR>
 * @date 2023/6/13
 */
public enum BindingCategoryEnum implements IEnum<String> {

    /**
     * 所有分类
     */
    AllCategory,

    /**
     * 指定分类
     */
    SpecifyCategory,

    ;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
