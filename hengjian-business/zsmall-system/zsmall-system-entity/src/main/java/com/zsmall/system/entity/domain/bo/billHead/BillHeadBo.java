package com.zsmall.system.entity.domain.bo.billHead;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.BillHead;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单-new业务对象 bill_head
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BillHead.class, reverseConvertGenerate = false)
public class BillHeadBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 供应商租户id
     */
    @NotBlank(message = "供应商租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 账单编号
     */
    @NotBlank(message = "账单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String billNo;

    /**
     * 上期账单编号
     */
    @NotBlank(message = "上期账单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String previousBillId;

    /**
     * 分销商-账单状态1 待确认 2确认中 3已确认
     */
    @NotNull(message = "分销商-账单状态1 待确认 2确认中 3已确认不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer billStatus;

    /**
     * 1 供应商 2分销商
     */
    @NotNull(message = "1 供应商 2分销商不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer billType;

    /**
     * 账单开始时间
     */
    @NotNull(message = "账单开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date billStartTime;

    /**
     * 账单结束时间
     */
    @NotNull(message = "账单结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date billEndTime;

    /**
     * 发货总金额(本期收入)
     */
    @NotNull(message = "发货总金额(本期收入)不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal totalShippedAmount;

    /**
     * 发货总数量
     */
    @NotNull(message = "发货总数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer totalShippedQuantity;

    /**
     * 退款总金额(本期支出)
     */
    @NotNull(message = "退款总金额(本期支出)不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal totalRefundAmount;

    /**
     * 退货总数量
     */
    @NotNull(message = "退货总数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer totalRefundQuantity;

    /**
     * 产品总金额
     */
    @NotNull(message = "产品总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal totalProductAmount;

    /**
     * 操作费总金额
     */
    @NotNull(message = "操作费总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal totalOperfeeAmount;

    /**
     * 尾程派送费总金额
     */
    @NotNull(message = "尾程派送费总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal totalDeliveryFeeAmount;

    /**
     * 本期订单总金额
     */
    @NotNull(message = "本期订单总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal currentOrderAmount;

    /**
     * 本期循环保证金(本期收入-本期支出)20%
     */
    @NotNull(message = "本期循环保证金(本期收入-本期支出)20%不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal currentRevolvingOrderAmount;

    /**
     * 上期订单总金额
     */
    @NotNull(message = "上期订单总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal previousOrderAmount;

    /**
     * 上期循环保证金
     */
    @NotNull(message = "上期循环保证金不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal previousRevolvingOrderAmount;

    /**
     * 本期总金额
     */
    @NotNull(message = "本期总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal totalAmount;


}
