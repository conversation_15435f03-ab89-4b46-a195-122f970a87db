package com.zsmall.extend.shop.amazon.job.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.redis.utils.QueueUtils;
import com.zsmall.extend.shop.amazon.bean.AmazonAppBean;
import com.zsmall.extend.shop.amazon.constants.ApiRate;
import com.zsmall.extend.shop.amazon.job.constants.AmznFeedCreateStatus;
import com.zsmall.extend.shop.amazon.job.constants.AmznQueueConstants;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncFeed;
import com.zsmall.extend.shop.amazon.job.entity.domain.JobAmznFeedSync;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IAmznSyncFeedService;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IJobAmznFeedSyncService;
import com.zsmall.extend.shop.amazon.job.mq.AmznJobRedisProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class AmazonFeedJobSupport extends JobBaseSupport {


    private final IAmznSyncFeedService amznSyncFeedService;
    private final IJobAmznFeedSyncService jobAmznFeedSyncService;
    private final AmznJobRedisProducer amznJobRedisProducer;


    /**
     * 任务分配至各个消费者队列
     */
    public void taskAssignment() {
        List<AmznSyncFeed> syncFeedList = amznSyncFeedService.selectWaitingTask();
        if(CollUtil.isNotEmpty(syncFeedList)) {
            syncFeedList.forEach(feed -> {
                AmznFeedCreateStatus feedStatus = feed.getFeedStatus();
                Long feedSyncId = feed.getId();
                // 获取队列名称
                String queueName = getQueueName(feedStatus);
                Long rateSeconds = RandomUtil.randomLong(10, 60);

                if(StrUtil.isBlank(queueName)) {
                    return;
                }
                // 判断队列是否存在当前值
                List<Long> feedSyncIds = QueueUtils.readAllDelayedQueueData(queueName);
                boolean has = CollUtil.isNotEmpty(feedSyncIds) && feedSyncIds.contains(feedSyncId);
                log.info("queueName = {}, feedSyncId = {}, rateSecond = {},  has ? {}", queueName, feedSyncId, rateSeconds, has);
                if(!has) {
                    // 随机时间，避免同时间出现太多任务
                    amznJobRedisProducer.publishProducer(queueName, feedSyncId, rateSeconds);
                }
            });
        }
    }

    private Double getRateSeconds(AmznFeedCreateStatus feedStatus) {
        Double seconds = 10.0;
        if(ObjectUtil.equals(feedStatus, AmznFeedCreateStatus.Todo)) {
            seconds = ApiRate.FeedsApi.createFeedDocumentWaitTimeInSeconds;
        } else if(ObjectUtil.equals(feedStatus, AmznFeedCreateStatus.FFDCreated)) {
            // Upload 这里是上传步骤，默认时间即可。
        } else if(ObjectUtil.equals(feedStatus, AmznFeedCreateStatus.Uploaded)) {
            seconds = ApiRate.FeedsApi.createFeedWaitTimeInSeconds;
        } else if(ObjectUtil.equals(feedStatus, AmznFeedCreateStatus.FeedCreated)) {
            seconds = ApiRate.FeedsApi.getFeedWaitTimeInSeconds;
        } else if(ObjectUtil.equals(feedStatus, AmznFeedCreateStatus.RFDCreated)) {
            seconds = ApiRate.FeedsApi.getFeedDocumentWaitTimeInSeconds;
        }
        return seconds;
    }

    private String getQueueName(AmznFeedCreateStatus status) {
        String queueName = null;
        if(ObjectUtil.equals(status, AmznFeedCreateStatus.Todo)) {
            queueName = AmznQueueConstants.FeedQueueName.TODO;
        } else if(ObjectUtil.equals(status, AmznFeedCreateStatus.FFDCreated)) {
            queueName = AmznQueueConstants.FeedQueueName.FFDCREATED;
        } else if(ObjectUtil.equals(status, AmznFeedCreateStatus.Uploaded)) {
            queueName = AmznQueueConstants.FeedQueueName.UPLOADED;
        } else if(ObjectUtil.equals(status, AmznFeedCreateStatus.FeedCreated)) {
            queueName = AmznQueueConstants.FeedQueueName.FEEDCREATED;
        } else if(ObjectUtil.equals(status, AmznFeedCreateStatus.RFDCreated)) {
            queueName = AmznQueueConstants.FeedQueueName.RFDCREATED;
        }
        return queueName;
    }

    /**
     * Feed上传 - 第一步：创建FeedDocument
     * @param feedSyncId
     */
    public void createFeedDocument(Long feedSyncId) {
        log.info("createFeedDocument feedSyncId = {}", feedSyncId);
        AmznSyncFeed syncFeed = amznSyncFeedService.getById(feedSyncId);
        Long jobSyncId = syncFeed.getJobSyncId();
        JobAmznFeedSync jobAmznFeedSync = jobAmznFeedSyncService.getById(jobSyncId);
        String amznAppId = jobAmznFeedSync.getAmznAppId();
        String sellerId = jobAmznFeedSync.getSellerId();
        String refreshToken = jobAmznFeedSync.getRefreshToken();

        AmazonAppBean amazonAppBean = buildAppBean(amznAppId);
        if (amazonAppBean == null) {
            log.error("sellerId = {}, amznAppId = {} is invalid...", sellerId, amznAppId);
            return;
        }

        // 更新状态
        syncFeed.setFeedStatus(AmznFeedCreateStatus.Processing);
        amznSyncFeedService.updateById(syncFeed);


    }

    /**
     * Feed上传 - 第二步：上传文件
     * @param feedSyncId
     */
    public void uploadFeed(Long feedSyncId) {
        log.info("uploadFeed feedSyncId = {}", feedSyncId);
    }

    /**
     * Feed上传 - 第三步：创建Feed
     * @param feedSyncId
     */
    public void createFeed(Long feedSyncId) {
        log.info("createFeed feedSyncId = {}", feedSyncId);
    }

    /**
     * Feed上传 - 第四步：获取FeedDocument
     * @param feedSyncId
     */
    public void getFeedDocument(Long feedSyncId) {
        log.info("getFeedDocument feedSyncId = {}", feedSyncId);
    }
    /**
     * Feed上传 - 第五步：更新FeedDocument
     * @param feedSyncId
     */
    public void updateFeedDocument(Long feedSyncId) {
        log.info("updateFeedDocument feedSyncId = {}", feedSyncId);
    }
}
