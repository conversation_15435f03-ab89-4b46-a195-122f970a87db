package com.zsmall.system.entity.domain.bo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-绑定wayfair账号")
public class ReqBillToWayfairBody {

    @Schema(title = "渠道id（编辑时传）")
    private Long id;

    @Schema(title = "渠道类型")
    private String channelName;

    @Schema(title = "渠道店铺名")
    private String channelShopName;

    @Schema(title = "各渠道通用的key字段")
    private String privateKey;

    @Schema(title = "渠道连接秘钥")
    private String clientSecret;

}
