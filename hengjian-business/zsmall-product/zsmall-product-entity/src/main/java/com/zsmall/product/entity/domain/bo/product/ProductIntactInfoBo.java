// ProductIntactInfoBo.java

package com.zsmall.product.entity.domain.bo.product;

import cn.hutool.json.JSONArray;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Request
 */
@Data
public class ProductIntactInfoBo {

    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    private String deliverGoodsTime;


    /**
     * 送达时间
     */
    @ApiModelProperty(value = "送达时间")
    private String deliveryTime;


    /**
     * 商品SPU名名称
     */
    private String productName;
    /**
     * 最底层分类id
     */
    private Long belongCategoryId;
    /**
     * 支持的物流，All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发
     */
    private String supportedLogistics;
    /**
     * 商品SPU描述
     */
    private String description;
    /**
     * 禁售渠道数组
     */
    private JSONArray forbiddenChannel;
    /**
     * 通用规格数组
     */
    private List<GenericSpecList> genericSpecList;
    /**
     * 可选规格数组
     */
    private List<OptionalSpecList> optionalSpecList;
    /**
     * 商品特色数组
     */
    private List<ProductFeatureList> productFeatureList;
    /**
     * 商品SKU数组
     */
    private List<ProductSkuList> productSkuList;
    /**
     * 其他附件
     */
    private ProductAttachmentBo otherAttachment;

    @Data
    public static class GenericSpecList {
        /**
         * 通用规格来源主键
         */
        private Long sourceId;
        /**
         * 通用规格key
         */
        private String key;
        /**
         * 通用规格value
         */
        private String value;
    }

    @Data
    public static class OptionalSpecList {
        /**
         * 可选规格来源主键
         */
        private Long sourceId;
        /**
         * 可选规格名
         */
        private String key;
        /**
         * 可选值数组
         */
        private JSONArray values;
    }

    @Data
    public static class ProductFeatureList {
        /**
         * 特色key
         */
        private String key;
        /**
         * 特色value
         */
        private String value;
    }

    @Data
    public static class ProductSkuList {
        /**
         * 最小库存单位
         */
        private String sku;
        /**
         * UPC代号
         */
        private String upc;
        /**
         * 系统库存单位
         */
        private String erpSku;
        /**
         * 商品单价，限制两位小数
         */
        private BigDecimal unitPrice;
        /**
         * 操作费，限制两位小数
         */
        private BigDecimal operationFee;
        /**
         * 尾程派送费，限制两位小数
         */
        private BigDecimal finalDeliveryFee;
        /**
         * 厂商建议零售价，限制两位小数
         */
        private BigDecimal msrp;
        /**
         * 库存管理方
         */
        private String stockManager;
        /**
         * 长
         */
        private String length;
        /**
         * 宽
         */
        private String width;
        /**
         * 高
         */
        private String height;
        /**
         * 长度单位
         */
        private String lengthUnit;
        /**
         * 重量
         */
        private String weight;
        /**
         * 重量单位
         */
        private String weightUnit;
        /**
         * 打包长度
         */
        private String packLength;
        /**
         * 打包宽度
         */
        private String packWidth;
        /**
         * 打包高度
         */
        private String packHeight;
        /**
         * 打包长度单位
         */
        private String packLengthUnit;
        /**
         * 打包重量
         */
        private String packWeight;
        /**
         * 打包重量单位
         */
        private String packWeightUnit;
        /**
         * 商品尺寸与打包尺寸是否一致
         */
        private boolean samePacking;
        /**
         * 运输方式
         */
        private String transportMethod;
        /**
         * 规格组成名称，示例：尺寸-大;颜色-白色
         */
        private String specComposeName;
        /**
         * 规格值名称，示例：大/白色
         */
        private String specValName;
        /**
         * 规格详细信息数组
         */
        private List<SpecComposeList> specComposeList;
        /**
         * 图片数组
         */
        private List<ProductAttachmentBo> imageList;
        /**
         * 视频数组
         */
        private List<ProductAttachmentBo> videoList;
        /**
         * 库存配置数组
         */
        private List<StockConfigList> stockConfigList;

        /**
         * 站点价格组
         */
        private List<ProductSitePriceBo> sitePriceBos;



    }

    @Data
    public static class SpecComposeList {
        /**
         * 规格来源主键
         */
        private Long sourceId;
        /**
         * 规格名
         */
        private String key;
        /**
         * 规格值
         */
        private String value;
    }

    @Data
    public static class StockConfigList {
        /**
         * 仓库系统编号
         */
        private String warehouseSystemCode;
        /**
         * 关联物流模板编号
         */
        private String logisticsTemplateNo;
        /**
         * 库存数量
         */
        private Integer quantity;
        /**
         * 一件代发库存数量 0单仓,1非单仓(等于自提库存)
         */
        private Integer proxyStockQuantity;

        /**
         * 代发库存标识 0单仓,1非单仓(等于自提库存)
         */
        private Integer dropShippingStockAvailable;
    }

}

