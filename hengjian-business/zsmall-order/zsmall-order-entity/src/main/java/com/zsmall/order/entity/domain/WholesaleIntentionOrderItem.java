package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 国外现货批发意向子订单
 * @TableName wholesale_intention_order_item
 */
@TableName(value ="wholesale_intention_order_item")
@Data
@EqualsAndHashCode(callSuper=false)
public class WholesaleIntentionOrderItem extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 供货商用户编号
     */
    private String supplierTenantId;

    /**
     * 关联子订单编号
     */
    private String orderItemNo;

    /**
     * 国外现货批发意向订单主键
     */
    private Long wholesaleIntentionOrderId;

    /**
     * 国外现货批发意向子订单编号
     */
    private String wholesaleIntentionOrderItemNo;

    /**
     * 子订单商品特有规格组合，JSON格式：{属性名:属性值}
     */
    private String ownSpec;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 国外现货商品SKU编号
     */
    private String productSkuCode;

    /**
     * 商品图片展示链接
     */
    private String imageShowUrl;

    /**
     * 商品图片存储地址
     */
    private String imageSavePath;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 订金比例
     */
    private BigDecimal depositRatio;

    /**
     * 商品单价
     */
    private BigDecimal unitPrice;

    /**
     * 商品单价（平台）
     */
    private BigDecimal unitPricePlatform;

    /**
     * 商品订金单价
     */
    private BigDecimal depositUnitPrice;

    /**
     * 商品订金单价（平台）
     */
    private BigDecimal depositUnitPricePlatform;

    /**
     * 商品尾款单价
     */
    private BigDecimal balanceUnitPrice;

    /**
     * 商品尾款单价（平台）
     */
    private BigDecimal balanceUnitPricePlatform;

    /**
     * 订金总金额
     */
    private BigDecimal depositTotalAmount;

    /**
     * 订金总金额（平台）
     */
    private BigDecimal depositTotalAmountPlatform;

    /**
     * 尾款总金额
     */
    private BigDecimal balanceTotalAmount;

    /**
     * 尾款总金额（平台）
     */
    private BigDecimal balanceTotalAmountPlatform;

    /**
     * 子订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 子订单总金额（平台）
     */
    private BigDecimal totalAmountPlatform;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
