package com.zsmall.system.entity.domain.bo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-08-09
 **/
@Data
@NoArgsConstructor
@Schema(name = "请求信息-临时表送货地址信息")
public class ReqTempShippingAddressBody {

    @Schema(title = "临时订单编号")
    private String tempOrderNo;

    @Schema(title = "收件人名称")
    private String recipientName;

    @Schema(title = "地址1")
    private String address1;

    @Schema(title = "地址2")
    private String address2;

    @Schema(title = "城市")
    private String city;

    @Schema(title = "洲的缩写")
    private String stateCode;

    @Schema(title = "手机号")
    private String phoneNumber;

    @Schema(title = "国家的缩写")
    private String countryCode;

    @Schema(title = "国家的缩写")
    private Long countryId;

    @Schema(title = "邮编")
    private String zipCode;

}
