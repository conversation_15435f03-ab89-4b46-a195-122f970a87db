package com.zsmall.extend.shop.rakuten.model.order;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 请求体-获取订单详情
 *
 * <AUTHOR>
 * @date 2023/10/13
 */
@Data
public class InGetOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /* 注文号列表 */
    private List<String> orderNumberList;

    /*
     * 版本号
     * 以下是每个版本的功能说明：
     *   3: 消费税增税支持
     *   4: 统一包邮线支持
     *   5: 收据、预付期限版
     *   6: 客户和配送注意事项详细支持
     *   7: SKU 支持
     * 请注意，这些描述可能只是简略的概述，并不能提供详细的功能细节。如果您需要更多关于每个版本功能的详细信息，建议参考相关文档或联系软件提供商以获取准确的说明。
     * */
    private Integer version = 7;

    public InGetOrder(List<String> orderNumberList) {
        this.orderNumberList = orderNumberList;
    }
}
