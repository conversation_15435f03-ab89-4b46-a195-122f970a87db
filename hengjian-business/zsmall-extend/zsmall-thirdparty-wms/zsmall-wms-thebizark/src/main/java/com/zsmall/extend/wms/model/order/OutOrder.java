package com.zsmall.extend.wms.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 响应输出 - 订单
 */
@Getter
@Setter
@Accessors(chain = true)
public class OutOrder {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付时间
     */
    private String paidAt;

    /**
     * 发运联系人
     */
    private String shipToContact;

    /**
     * 发运地址1
     */
    private String shipToAddress1;

    /**
     * 发运地址2
     */
    private String shipToAddress2;

    /**
     * 发运地址3
     */
    private String shipToAddress3;

    /**
     * 目的城市
     */
    private String shipToCity;

    /**
     * 目的州
     */
    private String shipToState;

    /**
     * 目的国家
     */
    private String shipToCountry;

    /**
     * 发运邮编
     */
    private String shipToPostal;

    /**
     * 发运联系电话
     */
    private String shipToTelephone;

    /**
     * 仓库code
     */
    private String warehouseCode;

    /**
     * carrier code
     */
    private String carrierCode;

    /**
     * third party 账号
     */
    private String thirdPartyAccount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 快递服务等级
     */
    private String shipServiceLevel;

    /**
     * 是否签收 0：否 1：是
     */
    private Integer isSignature;

    /**
     * 是否保价 0：否 1：是
     */
    private Integer isInsure;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 订单状态 pending:未支付,unshipped:未发货,approved:已审核,assigned:已分配,picked:已配货,shipped:已发货,canceled:已取消,
     * refunded:已退款,
     */
    private String orderStatusEnum;

    /**
     * 产品行信息
     */
    private List<OutProductItems> productItems;

    /**
     * 附件信息
     */
    private List<OutEnclosure> enclosures;

    /**
     * 发货信息
     */
    private List<OutShipment> shipmentPackages;

    /**
     * 最晚发货时间
     */
    private Date latestShipDate;

    /**
     * 仓库预计发货时间
     */
    private Date   warehouseExpectedShipDate;

}
