package com.zsmall.extend.pdf.exception;

public class PDFException extends BaseException {

  private static final long serialVersionUID = 5868056301676340675L;

  public PDFException() {
    super("PDF异常");
  }

  public PDFException(int errorCode, String errorMsg) {
    super(errorMsg);
    this.errorCode = errorCode;
    this.errorMsg = errorMsg;
  }

  public PDFException(String errorMsg) {
    super(errorMsg);
    this.errorCode = 500;
    this.errorMsg = errorMsg;
  }

  public PDFException(String errorMsg, Exception e) {
    super(errorMsg, e);
    this.errorCode = 500;
    this.errorMsg = errorMsg;
  }


}
