package com.zsmall.product.entity.domain.bo.product;

import com.zsmall.product.entity.domain.dto.productSku.ProductSkuSimpleBody;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求参数-商品SKU
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "请求参数-商品SKU")
public class ReqMassDeleteBody {

	@Schema(title = "SKU集合")
	private List<ProductSkuSimpleBody> skuList;

}
