<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncFeedFulfillmentShippingMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncFeedFulfillmentShipping">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="carrierCode" column="carrier_code" jdbcType="VARCHAR"/>
            <result property="carrierName" column="carrier_name" jdbcType="VARCHAR"/>
            <result property="shipperTrackingNumber" column="shipper_tracking_number" jdbcType="VARCHAR"/>
            <result property="shippingMethod" column="shipping_method" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carrier_code,carrier_name,
        shipper_tracking_number,shipping_method
    </sql>
</mapper>
