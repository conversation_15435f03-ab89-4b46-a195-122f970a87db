package com.zsmall.extend.shop.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ArrayUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 第三方店铺-Callback Controller
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/tps/callback")
public class TpsCallbackController {

    // 渠道类型集合
    private final String[] channelTypes = new String[] { "shopify" };
    private final String[] operateTypes = new String[] { "fulfillment" };


    /**
     * POST - 回调方法
     * @param httpServletRequest
     * @param httpServletResponse
     * @param channelType 渠道类型
     * @param operateType 操作类型：如履约等等
     */
    @SaIgnore
    @PostMapping(value = "{channelType}/{operateType}")
    public void callback(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                         @PathVariable String channelType, @PathVariable String operateType) {
        log.info("TpsCallback  channelType = {}, operateType = {}", channelType, operateType);

        if(!ArrayUtil.contains(channelTypes, channelType) || !ArrayUtil.contains(operateTypes, operateType)) {
            throw new RuntimeException("Channel type and operation type are not in the whitelist.");
        }

        // TODO 接收参数并处理对应业务逻辑

    }



}
