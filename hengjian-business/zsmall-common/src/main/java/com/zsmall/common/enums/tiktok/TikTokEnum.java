package com.zsmall.common.enums.tiktok;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 15:12
 */
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/10/10 10:21
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum TikTokEnum {

    AFTERSALE_APPLYING("reverse_order_status", 1, "售后-申请"),
    AFTERSALE_REJECT_APPLICATION("reverse_order_status", 2, "售后-拒绝申请"),
    AFTERSALE_RETURNING("reverse_order_status", 3, "售后-返回"),
    AFTERSALE_BUYER_SHIPPED("reverse_order_status", 4, "售后-买方发货"),
    AFTERSALE_SELLER_REJECT_RECEIVE("reverse_order_status", 5, "售后-卖家拒收"),
    AFTERSALE_SUCCESS("reverse_order_status", 50, "售后-成功"),
    CANCEL_SUCCESS("reverse_order_status", 51, "取消成功"),
    CLOSED("reverse_order_status", 99, "关闭"),
    COMPLETE("reverse_order_status", 100, "完成"),



    TIK_TOK_REFUND("tikTokShopRefundApi",null,"tiktok"),
    TIK_TOK_ORDER("tikTokShopOrderApi",null,"tiktok"),
    TIK_TOK_ORDER_V2("tikTokShopOrderApiV2",null,"tiktok"),
    TIK_TOK_PRODUCT_V2("tikTokShopProductApiV2",null,"tiktok");

    /**
     * 枚举类型
     */
    private String enumType;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 描述
     */
    private String desc;



}
