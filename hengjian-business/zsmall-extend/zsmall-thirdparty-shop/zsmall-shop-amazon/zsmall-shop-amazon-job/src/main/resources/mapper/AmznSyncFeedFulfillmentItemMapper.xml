<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncFeedFulfillmentItemMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncFeedFulfillmentItem">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="amazonOrderItemCode" column="amazon_order_item_code" jdbcType="VARCHAR"/>
            <result property="merchantFulfillmentItemId" column="merchant_fulfillment_item_id" jdbcType="BIGINT"/>
            <result property="merchantOrderItemId" column="merchant_order_item_id" jdbcType="VARCHAR"/>
            <result property="orderFulfillmentId" column="order_fulfillment_id" jdbcType="VARCHAR"/>
            <result property="quantity" column="quantity" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,amazon_order_item_code,merchant_fulfillment_item_id,
        merchant_order_item_id,order_fulfillment_id,quantity
    </sql>
</mapper>
