package com.zsmall.order.entity.domain;

import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.hengjian.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 订单异常处理记录对象 order_exception_handle_record
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_exception_handle_record")
@Accessors(chain = true)
public class OrderExceptionHandleRecord extends NoDeptTenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单主表id
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 异常类型
     */
    private Integer exceptionCode;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
