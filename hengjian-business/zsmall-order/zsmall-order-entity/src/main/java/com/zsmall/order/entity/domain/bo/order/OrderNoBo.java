package com.zsmall.order.entity.domain.bo.order;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求参数-订单编号数组
 *
 * <AUTHOR>
 * @create 2021/10/26 16:32
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OrderNoBo {

    /**
     * 订单编号数组
     */
    private List<String> orderIds;

    private String orderNo;

}
