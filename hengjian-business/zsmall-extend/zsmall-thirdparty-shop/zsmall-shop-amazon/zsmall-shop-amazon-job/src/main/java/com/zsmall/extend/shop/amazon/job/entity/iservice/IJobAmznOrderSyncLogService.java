package com.zsmall.extend.shop.amazon.job.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.extend.shop.amazon.job.entity.domain.JobAmznOrderSyncLog;
import com.zsmall.extend.shop.amazon.job.entity.mapper.JobAmznOrderSyncLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【job_amzn_order_sync_log(任务-日志-亚马逊订单同步)】的数据库操作Service实现
* @createDate 2023-11-01 12:09:42
*/
@Service
public class IJobAmznOrderSyncLogService extends ServiceImpl<JobAmznOrderSyncLogMapper, JobAmznOrderSyncLog> {

}




