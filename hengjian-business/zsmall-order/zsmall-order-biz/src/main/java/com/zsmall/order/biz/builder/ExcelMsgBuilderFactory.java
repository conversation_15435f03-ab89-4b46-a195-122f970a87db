package com.zsmall.order.biz.builder;

import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.zsmall.common.util.ExcelMsgBuilder;
import com.zsmall.order.entity.domain.vo.tracking.OrderItemTrackingVO;
import org.springframework.stereotype.Component;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/31 15:30
 */
@Component
public class ExcelMsgBuilderFactory {
    public ExcelMsgBuilder<OrderItemTrackingVO> createBuilder(ReadRowHolder rowHolder) {
        return new ExcelMsgBuilder<>(rowHolder,OrderItemTrackingVO.class);
    }
}
