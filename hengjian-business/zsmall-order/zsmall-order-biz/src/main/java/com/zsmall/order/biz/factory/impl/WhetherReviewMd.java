package com.zsmall.order.biz.factory.impl;

import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.constant.OrderRefundRuleConstant;
import com.zsmall.common.enums.order.OrderRefundStateType;
import com.zsmall.order.factory.RefundRuleFactory;
import com.zsmall.order.factory.RefundRuleService;
import com.zsmall.order.biz.support.RefundSupport;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.OrderRefundItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 是否需要员工审核
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@Slf4j
@Service("whetherReviewMd")
public class WhetherReviewMd implements RefundRuleService {

    @Autowired
    private RefundSupport refundSupport;

    @Override
    public void afterPropertiesSet() throws Exception {
        RefundRuleFactory.register("WhetherReviewMd", this);
    }

    /**
     * 售后规则“是否”类条件处理
     *
     * @param whetherValue
     * @param orderItem
     * @param orderRefund
     * @param orderRefundItem
     */
    @Override
    public void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo bo,
                                        OrderItem orderItem, OrderRefund orderRefund, OrderRefundItem orderRefundItem) {

        if (OrderRefundRuleConstant.WhetherReviewMd.NOT_REQUIRED.equals(whetherValue)) {
            orderRefund.setRefundState(OrderRefundStateType.Verifying);
        } else if (OrderRefundRuleConstant.WhetherReviewMd.REQUIRED.equals(whetherValue)) {
            orderRefund.setRefundState(OrderRefundStateType.ManagerVerifying);
        } else if (OrderRefundRuleConstant.WhetherReviewMd.THRESHOLD_EXCEEDED.equals(whetherValue)) {
            Boolean overStandard = refundSupport.refundRateOverStandard(orderItem, orderRefund.getPlatformRefundAmount());
            if (overStandard) {
                orderRefund.setRefundState(OrderRefundStateType.ManagerVerifying);
            } else {
                orderRefund.setRefundState(OrderRefundStateType.Verifying);
            }
        }

    }

    /**
     * 售后规则“是否”类条件处理（主订单适用）
     *
     * @param whetherValue
     * @param bo
     * @param order
     * @param orderItem
     * @param orderRefund
     * @param orderRefundItem
     */
    @Override
    public void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo bo, Orders order,
                                        OrderItem orderItem, OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException {

        if (OrderRefundRuleConstant.WhetherReviewMd.NOT_REQUIRED.equals(whetherValue)) {
            orderRefund.setRefundState(OrderRefundStateType.Verifying);
        } else if (OrderRefundRuleConstant.WhetherReviewMd.REQUIRED.equals(whetherValue)) {
            orderRefund.setRefundState(OrderRefundStateType.ManagerVerifying);
        } else if (OrderRefundRuleConstant.WhetherReviewMd.THRESHOLD_EXCEEDED.equals(whetherValue)) {
            Boolean overStandard = refundSupport.refundRateOverStandard(order, orderRefund);
            if (overStandard) {
                orderRefund.setRefundState(OrderRefundStateType.ManagerVerifying);
            } else {
                orderRefund.setRefundState(OrderRefundStateType.Verifying);
            }
        }

    }

}
