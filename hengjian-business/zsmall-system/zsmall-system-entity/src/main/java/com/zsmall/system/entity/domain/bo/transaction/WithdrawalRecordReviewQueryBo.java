package com.zsmall.system.entity.domain.bo.transaction;

import lombok.Data;

import java.util.List;

/**
 * 交易回执单-提现审核记录业务对象
 *
 * <AUTHOR> Li
 * @date 2023-06-21
 */
@Data
public class WithdrawalRecordReviewQueryBo {

    /**
     * 交易回执单号
     */
    private String paymentReceiptNo;

    /**
     * 提现帐号
     */
    private String receiptAccount;

    /**
     * 租户Id/供应商Id
     */
    private String tenantId;

    /**
     * 提现申请时间
     */
    private List<String> receiptApplyDate;

    /**
     * 提现到账时间
     */
    private List<String> receiptDestDate;

    /**
     * 收款方式
     */
    private String paymentMethodType;

    /**
     * 提现状态（全部-All，待审批-Pending，待转账-Processing，拒绝-Refused，已完成-Solved）
     */
    private String receiptStatus;


}
