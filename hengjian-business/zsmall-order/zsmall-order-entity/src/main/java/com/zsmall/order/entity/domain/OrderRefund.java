package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.common.InstantMessagingAppType;
import com.zsmall.common.enums.order.OrderRefundStateType;
import com.zsmall.common.enums.orderRefund.RefundAmountStateEnum;
import com.zsmall.common.enums.orderRefund.RefundApplyType;
import com.zsmall.common.enums.orderRefund.RefundDisputeStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 售后申请主单对象 order_refund
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_refund")
public class OrderRefund extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供货商租户编号
     */
    private String supplierTenantId;

    /**
     * 主订单表主键
     */
    private Long orderId;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 售后申请主单编号
     */
    private String orderRefundNo;

    /**
     * 原始退款金额（供货商）
     */
    private BigDecimal originalRefundAmount;

    /**
     * 平台退款金额（平台、分销商）
     */
    private BigDecimal platformRefundAmount;

    /**
     * 售后申请时间
     */
    private Date refundApplyTime;

    /**
     * 售后类型（仅退款、退货并且退款）
     */
    private RefundApplyType refundType;

    /**
     * 售后商品总数量
     */
    private Integer refundQuantity;

    /**
     * 售后规则编号
     */
    private String refundRuleNo;

    /**
     * 售后规则原因（JSON格式：{zh_CN: 中文原因, en_US: 英文原因}）
     */
    private String refundRuleReason;

    /**
     * 售后描述
     */
    private String refundDescription;

    /**
     * 退款金额状态（未退还、已退还）
     */
    private RefundAmountStateEnum refundAmountState;

    /**
     * 售后单状态
     */
    private OrderRefundStateType refundState;

    /**
     * 售后完成时间
     */
    private Date refundCompletionTime;

    /**
     * 售后争议状态
     */
    private RefundDisputeStateEnum refundDisputeState;

    /**
     * 联系方式Id
     */
    private String messagingAppId;

    /**
     * 联系方式APP
     */
    private InstantMessagingAppType messagingAppType;

    /**
     * 平台管理员用户主键
     */
    private String managerUserId;

    /**
     * 平台管理员审核时间
     */
    private Date managerReviewTime;

    /**
     * 平台管理员审核意见
     */
    private String managerReviewOpinion;

    /**
     * 供货商用户主键
     */
    private String supplierUserId;

    /**
     * 供货商审核时间
     */
    private Date supplierReviewTime;

    /**
     * 供货商审核意见
     */
    private String supplierReviewOpinion;
    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 币种符号
     */
    private String currencySymbol;

//    private String currencyCode;
//    private String currencySymbol;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private List<OrderRefundAttachment> attachments;

    @TableField(exist = false)
    private OrderRefundLogistics orderRefundLogistics;


}
