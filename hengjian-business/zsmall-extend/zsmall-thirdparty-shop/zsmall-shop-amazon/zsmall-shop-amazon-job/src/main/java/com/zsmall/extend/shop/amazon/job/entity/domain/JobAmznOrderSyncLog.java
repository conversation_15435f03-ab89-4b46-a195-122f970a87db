package com.zsmall.extend.shop.amazon.job.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zsmall.extend.shop.amazon.job.entity.bean.ErrorMessage;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务-日志-亚马逊订单同步
 * @TableName job_amzn_order_sync_log
 */
@TableName(value ="job_amzn_order_sync_log", autoResultMap = true)
@Data
public class JobAmznOrderSyncLog implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 订单同步主键
     */
    private Long orderSyncId;

    /**
     * 亚马逊应用Id
     */
    private String amznAppId;

    /**
     * 卖家Id
     */
    private String sellerId;

    /**
     * 亚马逊市场Id
     */
    private String marketplaceId;

    /**
     * 卖家授权token
     */
    private String refreshToken;

    /**
     * 任务同步开始时间
     */
    private Date syncStartTime;

    /**
     * 任务同步结束时间
     */
    private Date syncEndTime;

    /**
     * 同步状态, Todo/Running/Success/Error
     */
    private String syncStatus;

    /**
     * 错误信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ErrorMessage errorMessage;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        JobAmznOrderSyncLog other = (JobAmznOrderSyncLog) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderSyncId() == null ? other.getOrderSyncId() == null : this.getOrderSyncId().equals(other.getOrderSyncId()))
            && (this.getAmznAppId() == null ? other.getAmznAppId() == null : this.getAmznAppId().equals(other.getAmznAppId()))
            && (this.getSellerId() == null ? other.getSellerId() == null : this.getSellerId().equals(other.getSellerId()))
            && (this.getMarketplaceId() == null ? other.getMarketplaceId() == null : this.getMarketplaceId().equals(other.getMarketplaceId()))
            && (this.getRefreshToken() == null ? other.getRefreshToken() == null : this.getRefreshToken().equals(other.getRefreshToken()))
            && (this.getSyncStartTime() == null ? other.getSyncStartTime() == null : this.getSyncStartTime().equals(other.getSyncStartTime()))
            && (this.getSyncEndTime() == null ? other.getSyncEndTime() == null : this.getSyncEndTime().equals(other.getSyncEndTime()))
            && (this.getSyncStatus() == null ? other.getSyncStatus() == null : this.getSyncStatus().equals(other.getSyncStatus()))
            && (this.getErrorMessage() == null ? other.getErrorMessage() == null : this.getErrorMessage().equals(other.getErrorMessage()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderSyncId() == null) ? 0 : getOrderSyncId().hashCode());
        result = prime * result + ((getAmznAppId() == null) ? 0 : getAmznAppId().hashCode());
        result = prime * result + ((getSellerId() == null) ? 0 : getSellerId().hashCode());
        result = prime * result + ((getMarketplaceId() == null) ? 0 : getMarketplaceId().hashCode());
        result = prime * result + ((getRefreshToken() == null) ? 0 : getRefreshToken().hashCode());
        result = prime * result + ((getSyncStartTime() == null) ? 0 : getSyncStartTime().hashCode());
        result = prime * result + ((getSyncEndTime() == null) ? 0 : getSyncEndTime().hashCode());
        result = prime * result + ((getSyncStatus() == null) ? 0 : getSyncStatus().hashCode());
        result = prime * result + ((getErrorMessage() == null) ? 0 : getErrorMessage().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderSyncId=").append(orderSyncId);
        sb.append(", amznAppId=").append(amznAppId);
        sb.append(", sellerId=").append(sellerId);
        sb.append(", marketplaceId=").append(marketplaceId);
        sb.append(", refreshToken=").append(refreshToken);
        sb.append(", syncStartTime=").append(syncStartTime);
        sb.append(", syncEndTime=").append(syncEndTime);
        sb.append(", syncStatus=").append(syncStatus);
        sb.append(", errorMessage=").append(errorMessage);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
