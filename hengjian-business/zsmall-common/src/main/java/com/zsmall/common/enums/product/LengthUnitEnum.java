package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 长度单位类型
 */
@Getter
@AllArgsConstructor
public enum LengthUnitEnum implements IEnum<String> {

  /**
   * 毫米
   */
  mm(1000.0, "mm", "毫米"),

  /**
   * 厘米
   */
  cm(100.0, "cm", "厘米"),

  /**
   * 英寸
   */
  inch(39.3700787, "inch", "英寸"),

  /**
   * 分米
   */
  dm(10.0, "dm", "分米"),

  /**
   * 英尺
   */
  foot(3.2808399, "foot", "英尺"),

  /**
   * 米
   */
  m(1.0, "m", "米"),

  ;

  private double convert;
  private String unit;
  private String nameCn;

  public static LengthUnitEnum fromValue(double value) {
    for (LengthUnitEnum state : LengthUnitEnum.values()) {
      if (Objects.equals(value, state.getValue())) {
        return state;
      }
    }
    throw new IllegalArgumentException("Enum type does not exist!");
  }

  public static LengthUnitEnum fromUnit(String unit) {
    unit = unit.toLowerCase();
    for (LengthUnitEnum state : LengthUnitEnum.values()) {
      if (Objects.equals(unit, state.getUnit())) {
        return state;
      }
    }
    throw new IllegalArgumentException("Enum type does not exist!");
  }

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.unit;
    }
}
