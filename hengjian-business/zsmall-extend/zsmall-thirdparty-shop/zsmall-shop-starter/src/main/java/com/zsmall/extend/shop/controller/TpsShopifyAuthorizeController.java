package com.zsmall.extend.shop.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.extend.shop.model.vo.ConnectChannelVo;
import com.zsmall.extend.shop.service.TpsShopifyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 第三方店铺-Shopify授权Controller
 */
@Slf4j
@ConditionalOnExpression("${thirdparty-shop.shopify.enabled:true}")
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/tps/shopify")
public class TpsShopifyAuthorizeController {

    //    private final TpsBusinessShopifyService tpsBusinessShopifyService;
    private final TpsShopifyService tpsShopifyService;

    /**
     * 应用授权
     *
     * @param httpRequest
     * @param httpResponse
     */
    @SaIgnore
    @RequestMapping(value = "/authorization", method = RequestMethod.GET)
    public void authorization(HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
        tpsShopifyService.authorization(httpRequest, httpResponse);
    }

    /**
     * Shopify应用授权重定向
     *
     * @param requestBody
     * @param httpRequest
     * @param httpResponse
     * @return
     */
    @SaIgnore
    @RequestMapping(value = "/authorizeRedirect", method = RequestMethod.POST)
    public R<ConnectChannelVo> authorizeRedirect(@RequestBody JSONObject requestBody,
                                                 HttpServletRequest httpRequest, HttpServletResponse httpResponse) throws RStatusCodeException {
        ConnectChannelVo connectChannelVo = tpsShopifyService.authorizeRedirect(requestBody, httpRequest, httpResponse);
        return R.ok(connectChannelVo);
    }


}
