package com.zsmall.order.biz.test.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.biz.service.ProductActiveService;
import com.zsmall.common.enums.productActivity.ProductActivityExceptionEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.exception.StockException;
import com.zsmall.extend.wms.exception.ThebizarkException;
import com.zsmall.extend.wms.kit.ThebizarkDelegate;
import com.zsmall.extend.wms.kit.ThebizarkKit;
import com.zsmall.extend.wms.model.ThebizarkBean;
import com.zsmall.extend.wms.model.base.Result;
import com.zsmall.extend.wms.model.stock.OutStock;
import com.zsmall.order.biz.test.service.WarehouseServiceV2;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.product.entity.mapper.ProductMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseBizArkConfig;
import com.zsmall.warehouse.entity.iservice.IWarehouseBizArkConfigService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/18 17:59
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WarehouseServiceV2Impl implements WarehouseServiceV2 {
    private final IProductSkuService iProductSkuService;
    private final ProductMapper productMapper;
    private final ProductSupport productSupport;
    private final IProductSkuStockService iProductSkuStockService;
    private final IWarehouseBizArkConfigService iWarehouseBizArkConfigService;
    private final IWarehouseService iWarehouseService;
    private final ProductActiveService productActiveService;
//    private final IProductActivityStockService iProductActivityStockService;

    private ThebizarkDelegate initDelegate(String supplierId) {
        WarehouseBizArkConfig bizArkConfig = iWarehouseBizArkConfigService.queryBySupplierId(supplierId);
        return ThebizarkKit.create(new ThebizarkBean(bizArkConfig.getBizArkApiUrl(), bizArkConfig.getBizArkSecretKey(), bizArkConfig.getBizArkChannelAccount()));
    }

    /**
     * 功能描述：此任务后续需要重构 性能较低,频繁的连接数据库
     *
     * <AUTHOR>
     * @date 2024/03/20
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pullInventoryJob() {
        try {
            // 判断商品是否在BizArk仓库有库存
            // List<String> productSkuCodes 重复的sku只保留一个 用stream操作
            List<String> productSkuCodes = TenantHelper.ignore(productMapper::getAllBizArkProduct).stream()
                    .distinct() // 保留唯一元素
                    .collect(Collectors.toList());
            List<ProductSku> productSkus = TenantHelper.ignore(() -> iProductSkuService.list(new LambdaQueryWrapper<ProductSku>().in(ProductSku::getProductSkuCode, productSkuCodes)));
//        productSkus = productSkus.stream().filter(s -> s.getSku().equals("DOGF-8S01-24S"))
//                                              .collect(Collectors.toList());
//        Set<ProductSku> productSkuSet = productSkus.stream().collect(Collectors.toSet());
//
//        Set<String> skuCodesInProductSkus = productSkuSet.stream()
//                                                       .map(ProductSku::getProductSkuCode)
//                                                       .collect(Collectors.toSet());
//        List<String> collect = productSkuCodes.stream()
//                                              .filter(skuCode -> !skuCodesInProductSkus.contains(skuCode))
//                                              .collect(Collectors.toList());
//        log.info(JSON.toJSONString(collect));
            Map<String, Long> skuMap = productSkus.stream()
                    .collect(Collectors.toMap(
                            ProductSku::getProductSkuCode, // key
                            ProductSku::getId, // value
                            (existing, replacement) -> existing, // merge function, 可以选择如何处理重复的key
                            LinkedHashMap::new // 选择一个具体的Map实现，这里选择了LinkedHashMap以保持插入顺序
                    ));
            ThreadPoolExecutor executor = SpringUtil.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
            List<String> productSkuCodesAble = new ArrayList<>();
            LinkedHashMap<Long, Integer> stockTotalMap = new LinkedHashMap<>();
            CountDownLatch downLatch = new CountDownLatch(productSkus.size());
            Set<Map.Entry<String, Long>> entries = skuMap.entrySet();


            for (Map.Entry<String, Long> entry : entries) {
                executor.execute(() -> {
                    try {
                        // 调用第三方仓库查询库存 异步改同步
                        Integer stock = updateStock20240511(entry.getKey());
                        // 更新库存信息
//                    Integer stock  = 0;
                        if (ObjectUtil.isNotNull(stock)) {
                            stockTotalMap.put(entry.getValue(), stock);
                        }

                        productSkuCodesAble.add(entry.getKey());
                    } catch (Exception e) {
                        log.error("查询库存失败,productSkuCode={}", entry.getKey());
                    } finally {
                        downLatch.countDown();
                    }
                });
            }

            // skuMap 拿出所有的value 放入List集合中
            downLatch.await();
            List<Long> skuIds = new ArrayList<>(skuMap.values());
            // 批量更新
            log.info("erp仓库实际对应sku数量:{}", stockTotalMap.size());
            if (!stockTotalMap.isEmpty()) {
                int completedStock = iProductSkuService.updateStockTotalBatch(stockTotalMap);
                log.info("库存拉取任务执行完毕,需拉取库存数:{},已更新库存数:{}", productSkuCodes.size(), completedStock);
            }
            productSupport.createSyncTask(skuIds);
            if (productSkuCodesAble.size() < productSkus.size()) {
                log.info("拉取库存失败的商品:{}}", productSkus.stream().filter(s -> !productSkuCodesAble.contains(s))
                        .collect(Collectors.toList()));
            }
        } catch (InterruptedException e) {
            log.error("定时拉去库存业务异常，错误原因：" + e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取库存异步方法
     */
    @Async
    public void pullInventoryAsync() {
        // 判断商品是否在BizArk仓库有库存
        // List<String> productSkuCodes 重复的sku只保留一个 用stream操作
        List<String> productSkuCodes = TenantHelper.ignore(productMapper::getAllBizArkProduct).stream()
                                                   .distinct() // 保留唯一元素
                                                   .collect(Collectors.toList());
        List<ProductSku> productSkus = TenantHelper.ignore(() -> iProductSkuService.list(new LambdaQueryWrapper<ProductSku>().in(ProductSku::getProductSkuCode, productSkuCodes)));
//        productSkus = productSkus.stream().filter(s -> s.getSku().equals("DOGF-8S01-24S"))
//                                              .collect(Collectors.toList());
//        Set<ProductSku> productSkuSet = productSkus.stream().collect(Collectors.toSet());
//
//        Set<String> skuCodesInProductSkus = productSkuSet.stream()
//                                                       .map(ProductSku::getProductSkuCode)
//                                                       .collect(Collectors.toSet());
//        List<String> collect = productSkuCodes.stream()
//                                              .filter(skuCode -> !skuCodesInProductSkus.contains(skuCode))
//                                              .collect(Collectors.toList());
//        log.info(JSON.toJSONString(collect));
        Map<String, Long> skuMap = productSkus.stream()
                                              .collect(Collectors.toMap(
                                                  ProductSku::getProductSkuCode, // key
                                                  ProductSku::getId, // value
                                                  (existing, replacement) -> existing, // merge function, 可以选择如何处理重复的key
                                                  LinkedHashMap::new // 选择一个具体的Map实现，这里选择了LinkedHashMap以保持插入顺序
                                              ));
        ThreadPoolExecutor executor = SpringUtil.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
        List<String> productSkuCodesAble = new ArrayList<>();
        LinkedHashMap<Long, Integer> stockTotalMap = new LinkedHashMap<>();
        CountDownLatch downLatch = new CountDownLatch(productSkus.size());
        Set<Map.Entry<String, Long>> entries = skuMap.entrySet();


        for (Map.Entry<String, Long> entry : entries) {
            executor.execute(() -> {
                try {
                    // 调用第三方仓库查询库存 异步改同步
                    Integer stock = updateStock20240511(entry.getKey());
                    // 更新库存信息
//                    Integer stock  = 0;
                    if (ObjectUtil.isNotNull(stock)) {
                        stockTotalMap.put(entry.getValue(), stock);
                    }

                    productSkuCodesAble.add(entry.getKey());
                } catch (Exception e) {
                    log.error("查询库存失败,productSkuCode={}", entry.getKey());
                } finally {
                    downLatch.countDown();
                }
            });
        }
        try {
            // skuMap 拿出所有的value 放入List集合中
            downLatch.await();
            List<Long> skuIds = new ArrayList<>(skuMap.values());
            // 批量更新
            log.info("erp仓库实际对应sku数量:{}", stockTotalMap.size());
            if (!stockTotalMap.isEmpty()){
                int completedStock = iProductSkuService.updateStockTotalBatch(stockTotalMap);
                log.info("库存拉取任务执行完毕,需拉取库存数:{},已更新库存数:{}", productSkuCodes.size(), completedStock);
            }
            productSupport.createSyncTask(skuIds);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        if (productSkuCodesAble.size() < productSkus.size()) {
            log.info("拉取库存失败的商品:{}}", productSkus.stream().filter(s -> !productSkuCodesAble.contains(s))
                                                          .collect(Collectors.toList()));
        }
    }

    /**
     * 功能描述：更新库存
     *
     * @param productSkuCode 产品sku代码
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/03/21
     */
    public Integer updateStock20240511(String productSkuCode) {
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        try {
            if (ObjectUtil.isEmpty(productSku)) {
                throw new ThebizarkException(1001, "商品不存在" + productSkuCode);
            }
            String supplierId = productSku.getTenantId();
            if (StrUtil.isBlank(supplierId)) {
                throw new ThebizarkException(1001, "供货商租户编号为空");
            }
            String erpSku = productSku.getSku();
            if (StrUtil.isBlank(erpSku)) {
                throw new ThebizarkException(1001, "SKU不存在");
            }

            LambdaQueryWrapper<ProductSkuStock> lqw = Wrappers.lambdaQuery();
            lqw.eq(ProductSkuStock::getProductSkuCode, productSkuCode);
            lqw.eq(ProductSkuStock::getTenantId, productSku.getTenantId());
            List<ProductSkuStock> stocks = TenantHelper.ignore(() -> iProductSkuStockService.getBaseMapper()
                                                                                            .selectList(lqw));
            Map<String, ProductSkuStock> stockMap = stocks.stream().collect(Collectors.toMap(
                stock -> productSku.getSku(),
                Function.identity()
            ));

            //查询商品的product_sku_code信息查询所属的仓库】
            List<Warehouse> warehouseListByProductSkuCode =   TenantHelper.ignore(()->iWarehouseService.getWarehouseByProductSkuCode(productSkuCode));
            log.info(productSkuCode+" 商品所属仓库" + JSON.toJSONString(warehouseListByProductSkuCode));
            if (ObjectUtil.isEmpty(warehouseListByProductSkuCode)) {
                throw new ThebizarkException(101309, "商品所属的仓库信息不存在,商品编码：" + productSkuCode);
            }
            //商品唯一码关联的仓库
            Map<String, Warehouse> warehouseMap = warehouseListByProductSkuCode.stream()
                                                                               .collect(Collectors.toMap(Warehouse::getWarehouseSystemCode, Function.identity()));
            erpSku = productSku.getSku().replace(" ", "");
            List<String> erpSkuList;
            if (StrUtil.isBlank(erpSku)) {
                erpSkuList = CollUtil.newArrayList(productSku.getSku());
            } else {
                erpSkuList = StrUtil.split(erpSku, ";");
            }
            ThebizarkDelegate delegate = initDelegate(supplierId);
            List<OutStock> outStockList = null;

            log.info("恒健仓库【查询库存】 supplierId = {} erpSkuList = {}", supplierId, JSONUtil.toJsonStr(erpSkuList));
            Result<List<OutStock>> result = delegate.stockApi().getInventory(erpSkuList);
            log.info("恒健仓库【查询库存】 查询库存结果 = {}", JSONUtil.toJsonStr(result));
            if (result.getStatusCode() != null && result.getStatusCode() == 200) {
                outStockList = result.getData();
            }
            if (CollUtil.isNotEmpty(outStockList)) {
                // 根据恒健仓库的文档筛选出可售库存
                outStockList = outStockList.stream().filter(outStock -> outStock.getInventoryType() == 0)
                                           .collect(Collectors.toList());
                //转为Map
                //根据warehouseCode 聚合去重
                HashMap<String, OutStock> outStockHashMap = groupByWarehouseCode(outStockList);


                // 后续 产品梳理做planB
                //   String code = "EFCA";
                //商品所属的仓库
                for (String code : warehouseMap.keySet()) {
//                    if (!ObjectUtil.equals("GA296933",code)){
//                        continue;
//                    }
                    OutStock outStock = outStockHashMap.get(code);
                    Warehouse warehouse = warehouseMap.get(code);
                    ProductSkuStock productSkuStock = stockMap.get(productSku.getProductSkuCode() + warehouse.getWarehouseSystemCode());

                    if (ObjectUtil.isEmpty(outStock)) {
                        // 如果没有匹配到外部库存数据，将库存设置为0
                        log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 未匹配到外部库存，设置库存为0", productSkuCode, code);
                        iProductSkuStockService.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouse.getWarehouseSystemCode(), 0, null);
                        productSupport.updateProductInventoryPushTime(productSkuCode);
                        // 因为是具体仓库现在没有库存，根据商品+仓库 匹配分销商活动
                        if (ObjectUtil.isNotNull(productSkuStock)){
                            productActiveService.updateProductActivityStockException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(),productSkuStock.getId());
                            if (productSkuStock.getPickupLockUsed()>0 || productSkuStock.getDropShippingLockUsed()>0){
                                //当前商品参与了活动，将改库存设置为异常
                                productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                                TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                            }
                        }
                    }else {
                        // 如果匹配到外部库存数据，使用外部库存数量
                        int  totalInventory = outStock.getInventoryAvailable();
                        //是否单仓
                        String singleWarehouseFlag = outStock.getSingleWarehouseFlag();
                        int sw = ObjectUtil.equal("true", singleWarehouseFlag) ? 0 : 1;
                        //判断数量,获取库存信息，这里采用

                        if (ObjectUtil.isNotEmpty(productSkuStock)){
                            //如果是单仓
                            if (sw == 0){
                                //先判断当前仓库有没有代发模式的锁货活动，如果有将供应商/分销商活动设置为异常，库存不更新
                                if (productSkuStock.getDropShippingLockUsed()>0){
                                    log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 有代发锁货活动，设置库存为异常", productSkuCode, code);
                                    productActiveService.updateProductActivityStockException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(),productSkuStock.getId());
                                    productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                                    TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                }else {
                                    if (totalInventory-productSkuStock.getPickupLockUsed() > 0){
                                        log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 最新ERP推送数量 = {}", productSkuCode, code, totalInventory);
                                        //根据商品SKU唯一编号和仓库编号更新库存
                                        iProductSkuStockService.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouse.getWarehouseSystemCode(), totalInventory-productSkuStock.getPickupLockUsed(), sw);
                                        //更新product表的库存推送时间
                                        productSupport.updateProductInventoryPushTime(productSkuCode);
                                        //将这个商品关联的所有活动设置为正常
                                        productActiveService.updateProductActivityStockException(1,productSkuStock.getId());
                                        productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
                                        TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                    }else {
                                        log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 有自提锁货活动，设置库存为异常", productSkuCode, code);
                                        productActiveService.updateProductActivityStockException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(), productSkuStock.getId());
                                        productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                                        TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                    }
                                }

                            }
                            //非单仓
                            if (sw == 1){
                                if (totalInventory-productSkuStock.getPickupLockUsed()-productSkuStock.getDropShippingLockUsed() > 0){
                                    log.info("同步恒健仓库库存 Item No. {} 仓库编码 {} 最新ERP推送数量 = {}", productSkuCode, code, totalInventory);
                                    //根据商品SKU唯一编号和仓库编号更新库存
                                    iProductSkuStockService.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouse.getWarehouseSystemCode(), totalInventory-productSkuStock.getPickupLockUsed()-productSkuStock.getDropShippingLockUsed(), sw);
                                    //更新product表的库存推送时间
                                    productSupport.updateProductInventoryPushTime(productSkuCode);
                                    //将这个商品关联的所有活动设置为正常
                                    productActiveService.updateProductActivityStockException(ProductActivityExceptionEnum.NOT_EXCEPTION.getCode(),productSkuStock.getId());
                                    productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
                                    TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                }else {
                                    productActiveService.updateProductActivityStockException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(),productSkuStock.getId());
                                    productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                                    TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                                }
                            }
                        }
                    }
                }
                productActiveService.updateProductActivityExceptionByStock(productSkuCode);
            }else {
                log.info("恒健仓库【查询库存】 查询库存结果为空");
                //将当前品所属的仓库数量全部更新为0
                warehouseMap.forEach((key, warehouse) -> {
                    //根据商品SKU唯一编号和仓库编号更新库存
                    iProductSkuStockService.updateByProductSkuCodeAndWarehouseV2(productSkuCode, warehouse.getWarehouseSystemCode(), 0,null);
                    //更新product表的库存推送时间
                    productSupport.updateProductInventoryPushTime(productSkuCode);
                    ProductSkuStock productSkuStock = stockMap.get(productSku.getProductSkuCode() + warehouse.getWarehouseSystemCode());
                    if (ObjectUtil.isEmpty(productSkuStock)){
                        if (productSkuStock.getPickupLockUsed()>0 || productSkuStock.getDropShippingLockUsed()>0){
                            //当前商品参与了活动，将改库存设置为异常
                            productSkuStock.setLockExceptionCode(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode());
                            TenantHelper.ignore(()->iProductSkuStockService.updateById(productSkuStock)) ;
                        }
                    }

                });
                //将这个商品关联的所有活动设置为异常
                productActiveService.updateProductActivityException(ProductActivityExceptionEnum.STOCK_PULL_LOCK_EXCEPTION.getCode(),productSkuCode,null);
            }
            // 全库存 包含 自有-恒健
            return iProductSkuStockService.sumStockTotal(productSkuCode);
        } catch (Exception e) {
            log.error("恒健仓库【查询库存】 查询库存发生异常 {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 处理List<OutStock> 相同的warehouseCode 对InventoryAvailable相加
     *
     * @param outStockLists
     * @return
     */
    public static HashMap<String, OutStock> groupByWarehouseCode(List<OutStock> outStockLists) {
        // 使用Stream API进行操作
        Map<String, List<OutStock>> groupedByWarehouseSystemCode = outStockLists.stream()
                                                                                .collect(Collectors.groupingBy(OutStock::getWarehouseSystemCode));

        // 将Map转换回List<OutStock>，选择第一个出现的OutStock对象作为代表，并更新它的inventoryAvailable字段
        HashMap<String, OutStock> outStockMap = new HashMap<>();
        for (Map.Entry<String, List<OutStock>> entry : groupedByWarehouseSystemCode.entrySet()) {
            List<OutStock> outStockList = entry.getValue();
            OutStock representativeOutStock = outStockList.get(0); // 选择第一个对象作为代表
            int totalInventoryAvailable = outStockList.stream().mapToInt(OutStock::getInventoryAvailable).sum();
            representativeOutStock.setInventoryAvailable(totalInventoryAvailable); // 更新库存数量
            outStockMap.put(entry.getKey(), representativeOutStock);
        }
        return outStockMap;
    }

    /**
     * 功能描述：更新库存
     *
     * @param productSkuCode 产品sku代码
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/03/21
     */
    public Integer updateStock(String productSkuCode) {
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        try {
            String supplierId = productSku.getTenantId();
            if (StrUtil.isBlank(supplierId)) {
                throw new ThebizarkException(1001, "供货商租户编号为空");
            }
            log.info("恒健仓库 - supplierId = {} 商品SKU查询库存 = {}", supplierId, productSkuCode);

            if (productSku != null) {
                String erpSku = productSku.getSku().replace(" ", "");
                List<String> erpSkuList;
                if (StrUtil.isBlank(erpSku)) {
                    erpSkuList = CollUtil.newArrayList(productSku.getSku());
                } else {
                    erpSkuList = StrUtil.split(erpSku, ";");
                }
                ThebizarkDelegate delegate = initDelegate(supplierId);
                List<OutStock> outStockList = null;

                log.info("恒健仓库【查询库存】 supplierId = {} erpSkuList = {}", supplierId, JSONUtil.toJsonStr(erpSkuList));
                Result<List<OutStock>> result = delegate.stockApi().getInventory(erpSkuList);
                log.info("恒健仓库【查询库存】 查询库存结果 = {}", JSONUtil.toJsonStr(result));
                if (result.getStatusCode() != null && result.getStatusCode() == 200) {
                    outStockList = result.getData();
                }

                if (CollUtil.isNotEmpty(outStockList)) {
                    // 根据恒健仓库的文档筛选出可售库存
                    outStockList = outStockList.stream().filter(outStock -> outStock.getInventoryType() == 0)
                                               .collect(Collectors.toList());
                    BigDecimal totalInventory = BigDecimal.ZERO;
                    BigDecimal stockQuantity = BigDecimal.ZERO;
                    // 后续 产品梳理做planB
                    String code = "EFCA";
                    Warehouse warehouse = TenantHelper.ignore(() -> iWarehouseService.queryByWarehouseType(supplierId, WarehouseTypeEnum.BizArk, code));
                    if (ObjectUtil.isEmpty(warehouse) || ObjectUtil.isEmpty(warehouse.getWarehouseSystemCode())) {
                        throw new StockException(ZSMallStatusCodeEnum.STOCK_STATUS_UNKNOWN);
                    }
                    for (OutStock outStock : outStockList) {
                        Integer inventoryAvailable = outStock.getInventoryAvailable();

                        if (warehouse != null) {

                            totalInventory = totalInventory.add(BigDecimal.valueOf(inventoryAvailable));


                        }
                    }
                    // 只减去一次库存
//                    Integer stockOccupation = iProductActivityStockService.queryActivityStockOccupation(productSkuCode, warehouse.getWarehouseSystemCode());
//                    log.info("同步恒健仓库库存 Item No. {} 原可用数量 = {} 活动库存占用数量 = {}", productSkuCode, totalInventory, stockOccupation);

//                    if (totalInventory.subtract(BigDecimal.valueOf(stockOccupation)).compareTo(BigDecimal.ZERO) > 0) {
//                        BigDecimal inventory = totalInventory.multiply(BigDecimal.valueOf(0.7))
//                                                             .subtract(BigDecimal.valueOf(500));
//                        if (inventory.compareTo(BigDecimal.valueOf(0)) > 0) {
//                            stockQuantity = inventory.setScale(0, RoundingMode.UP);
//                        }
//                    }
                    log.info("同步恒健仓库库存 Item No. {} 新可用数量 = {}", productSkuCode, stockQuantity);
                    iProductSkuStockService.updateByProductSkuCodeAndWarehouse(productSkuCode, code, stockQuantity.intValue());
                    // 全库存 包含 自有-恒健
                    Integer stockTotal = iProductSkuStockService.sumStockTotal(productSkuCode);
                    return stockTotal;
                }
            }
        } catch (Exception e) {
            log.error("恒健仓库【查询库存】 查询库存发生异常 {}", e.getMessage(), e);
        }
        return null;
    }
}
