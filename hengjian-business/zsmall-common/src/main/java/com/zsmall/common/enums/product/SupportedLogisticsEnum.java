package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import lombok.AllArgsConstructor;

import java.util.Locale;

/**
 * 支持的物流类型枚举
 *
 * <AUTHOR>
 * @date 2023/5/29
 */
@AllArgsConstructor
public enum SupportedLogisticsEnum implements IEnum<String> {

    /**
     * 都支持
     */
    All("一件代发、自提都支持", "DropShipping & PickUp Supported Both", "全て対応"),

    /**
     * 仅支持自提
     */
    PickUpOnly("仅支持自提", "PickUp Supported Only", "自己引取りのみ対応"),

    /**
     * 仅支持代发
     */
    DropShippingOnly("仅支持代发", "DropShipping Supported Only", "代理発送のみ対応"),

    ;

    private String zh_CN;

    private String en_US;

    private String ja_JP;

    public String getByLocale(String language) {
        if (Locale.SIMPLIFIED_CHINESE.toString().equals(language)) {
            return zh_CN;
        } else if (Locale.JAPAN.toString().equals(language)) {
            return ja_JP;
        } else {
            return en_US;
        }
    }

    /**
     * 判断当前支持的类型是否允许发货
     * @param logisticsType
     * @return
     */
    public boolean allowShipping(LogisticsTypeEnum logisticsType) {
        if (PickUpOnly.equals(this)) {
            if (LogisticsTypeEnum.PickUp.equals(logisticsType)) {
                return true;
            } else {
                return false;
            }
        } else if (DropShippingOnly.equals(this)) {
            if (LogisticsTypeEnum.DropShipping.equals(logisticsType)) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
