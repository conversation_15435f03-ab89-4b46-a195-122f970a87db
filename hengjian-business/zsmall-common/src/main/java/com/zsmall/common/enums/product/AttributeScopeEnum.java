package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 属性作用域类型
 */
public enum AttributeScopeEnum implements IEnum<String> {

    /**
     * 所有场景
     */
    All,

    /**
     * 通用规格
     */
    GenericSpec,

    /**
     * 可选规格
     */
    OptionalSpec

    ;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
