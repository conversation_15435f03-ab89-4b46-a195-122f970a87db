package com.zsmall.system.entity.domain.bo.settleInBasic;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.TenantSupSettleInContact;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 供应商入驻公司联系人信息业务对象 tenant_sup_settle_in_contact
 *
 * <AUTHOR> Li
 * @date 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantSupSettleInContact.class, reverseConvertGenerate = false)
public class TenantSupSettleInContactBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 姓
     */
    @NotBlank(message = "姓不能为空", groups = {AddGroup.class, EditGroup.class})
    private String firstName;

    /**
     * 名
     */
    @NotBlank(message = "名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String lastName;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 手机号码国际区号
     */
    @NotBlank(message = "手机号码国际区号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String areaCode;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String phoneNumber;

    /**
     * email
     */
    @NotBlank(message = "email不能为空", groups = {AddGroup.class, EditGroup.class})
    private String email;

    /**
     * 通讯软件类型
     */
    @NotNull(message = "通讯软件类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long msgAppType;

    /**
     * 通讯软件账号
     */
    @NotBlank(message = "通讯软件账号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String msgAppAccount;

    /**
     * 国家id
     */
    @NotNull(message = "国家id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long countryId;

    /**
     * 省/州id
     */
    @NotNull(message = "省/州id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long stateId;

    /**
     * 省/州文本
     */
    @NotBlank(message = "省/州文本不能为空", groups = {AddGroup.class, EditGroup.class})
    private String stateText;

    /**
     * 城市id
     */
    @NotNull(message = "城市id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long cityId;

    /**
     * 城市文本
     */
    @NotBlank(message = "城市文本不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cityText;

    /**
     * 具体地址
     */
    @NotBlank(message = "具体地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String address;

    /**
     * 联系人类型
     */
    @NotNull(message = "联系人类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long contactType;

    /**
     * 用户编码
     */
    @NotBlank(message = "用户编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String creatorCode;

    /**
     * 供应商入驻基础信息id
     */
    @NotNull(message = "供应商入驻基础信息id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long basicId;


}
