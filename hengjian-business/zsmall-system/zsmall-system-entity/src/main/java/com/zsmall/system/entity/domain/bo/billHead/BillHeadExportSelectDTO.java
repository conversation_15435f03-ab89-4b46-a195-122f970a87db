package com.zsmall.system.entity.domain.bo.billHead;

import lombok.Data;
import java.util.List;

@Data
public class BillHeadExportSelectDTO {
    //账单类型: 1 供应商 2分销商
    private Integer billType;
    //账单编号
    private String billNo;
    //租户ID
    private String tenantId;
    //分销商-账单状态: 1 待确认 2确认中 3已确认
    private Integer billStatus;
    //账单开始时间
    private String billStartTime;
    //账单结束时间
    private String billEndTime;
    //勾选的账单编号
    private List<Long>  billHeadIds;
    //币种
    private String currencyCode;
}
