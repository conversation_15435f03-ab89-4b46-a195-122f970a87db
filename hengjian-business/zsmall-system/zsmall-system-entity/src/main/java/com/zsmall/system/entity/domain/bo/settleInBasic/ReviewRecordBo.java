package com.zsmall.system.entity.domain.bo.settleInBasic;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求参数-供应商入驻基础信息
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ReviewRecordBo {

    /**
     *
     */
    private String queryType;
    /**
     *
     */
    private String queryValue;
    /**
     * 状态
     */
    private String state;
    /**
     * 排序类型
     */
    private String sortType;
    /**
     * 时间选择
     */
    private List<String> searchDates;
    /**
     * 时间选择
     */
    private String searchDate;
    /**
     * 供应商租户ID
     */
    private String tenantId;
    /**
     * 审核理由（拒绝时使用）
     */
    private String reviewReason;

    /**
     *  租户类型
     */
    private String tenantType;

    /**
     * 商品源类型: 国内现货 ChinaSpotProducts, 海外仓现货 OverseasSpotProducts
     */
//    private String productSourceType;


}
