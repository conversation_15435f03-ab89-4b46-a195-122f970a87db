package com.zsmall.warehouse.entity.domain.resp;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/30 14:04
 */
@Data
public class WarehouseInventoryResp {
    private static final long serialVersionUID = 1L;


    private Integer id;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 仓库id
     */
    private Integer orgWarehouseId;

    /**
     * 仓库code
     */
    private String orgWarehouseCode;

    /**
     * sku
     */
    private String sku;

    /**
     * 类别id
     */
    private Integer categoryId;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 预警类型：1.单仓预警
     * 单仓预警临时方案，后期正式方案上线可删除
     */
    private Integer warningType;

    /**
     * 现库库存数量
     */
    private Integer currentInventory=0;

    /**
     * 在途库存数量
     */
    private Integer transportInventory=0;

    /**
     * 工厂库存数量
     */
    private Integer factoryInventory=0;

    /**
     * 第一周预计到货数
     */
    private Integer estimatedArrivalQuantityWk1=0;

    /**
     * 第二周预计到货数
     */
    private Integer estimatedArrivalQuantityWk2=0;

    /**
     * 第三周预计到货数
     */
    private Integer estimatedArrivalQuantityWk3=0;

    /**
     * 第四周预计到货数
     */
    private Integer estimatedArrivalQuantityWk4=0;

    /**
     * 第五周预计到货数
     */
    private Integer estimatedArrivalQuantityWk5=0;

    /**
     * 第六周预计到货数
     */
    private Integer estimatedArrivalQuantityWk6=0;

    /**
     * 第七周预计到货数
     */
    private Integer estimatedArrivalQuantityWk7=0;

    /**
     * 第八周预计到货数
     */
    private Integer estimatedArrivalQuantityWk8=0;

    /**
     * 第九周预计到货数
     */
    private Integer estimatedArrivalQuantityWk9=0;

    /**
     * 第十周预计到货数
     */
    private Integer estimatedArrivalQuantityWk10=0;

    /**
     * 第十一周预计到货数
     */
    private Integer estimatedArrivalQuantityWk11=0;

    /**
     * 第十二周预计到货数
     */
    private Integer estimatedArrivalQuantityWk12=0;

    /**
     * 第十三周预计到货数
     */
    private Integer estimatedArrivalQuantityWk13=0;

    /**
     * 第十四周预计到货数
     */
    private Integer estimatedArrivalQuantityWk14=0;
    /**
     * 第十五周预计到货数
     */
    private Integer estimatedArrivalQuantityWk15=0;
    /**
     * 第十六周预计到货数
     */
    private Integer estimatedArrivalQuantityWk16=0;
    /**
     * 第十七周预计到货数
     */
    private Integer estimatedArrivalQuantityWk17=0;
    /**
     * 第十八周预计到货数
     */
    private Integer estimatedArrivalQuantityWk18=0;
    /**
     * 第十九周预计到货数
     */
    private Integer estimatedArrivalQuantityWk19=0;
    /**
     * 第二十周预计到货数
     */
    private Integer estimatedArrivalQuantityWk20=0;
    /**
     * 同步时间
     */
    private LocalDateTime synchronizeTime;


    /**
     * 产品类型 1，成品；2，配件；3，耗材
     */
    private Integer productType;

    /**
     * sku 仓库维度数据
     */

//    private List<DwsInventoryArrivalPlanInfoDetailVO> childList;


    /**
     * 当月预计到货量
     */
    private Integer estimatedArrivalQuantityMth1=0;

    /**
     * 未来第一月预计到货量
     */
    private Integer estimatedArrivalQuantityMth2=0;

    /**
     * 未来第二月预计到货量
     */
    private Integer estimatedArrivalQuantityMth3=0;

    /**
     * 未来第三月预计到货量
     */
    private Integer estimatedArrivalQuantityMth4=0;

    /**
     * 未来第四月预计到货量
     */
    private Integer estimatedArrivalQuantityMth5=0;


    /**
     * 7天日销
     */
    private Integer avgSalesDay7;

    /**
     * 15天日销
     */
    private Integer avgSalesDay15;

    /**
     * 30天日销
     */
    private Integer avgSalesDay30;

    /**
     * 产品定位
     */
    private Integer productTarget;

    /**
     * 7天日均发货量
     */
    private Integer avgDeliveryDay7;

    /**
     * 15天日均发货量
     */
    private Integer avgDeliveryDay15;

    /**
     * 30天日均发货量
     */
    private Integer avgDeliveryDay30;

    /**
     * 产品定位名称 冗余字段
     */

    private String productTargetName;

    /**
     * 全链路库存合计
     */

    private Integer totalInventoryOfTheEntireChain = 0;

    /**
     * 全链路周转合计字段 例如 100|20|5 下同
     */

    private String totalFullChainTurnover;

    /**
     * 现库周转
     */

    private String currentInventoryTurnover;

    /**
     * 现库+在途周转
     */

    private String currentInventoryInTransitTurnover;

    /**
     * 工厂周转
     */

    private String factoryTurnover;

    /**
     * 中心仓名称
     */

    private String centralWarehouseName;


    /**
     * 层级
     */

    private Integer hierarchy;


    private Integer day30;

    /**
     * 单仓预警临时方案，后期正式方案上线可删除
     */

    private String warningTypeName;
}
