package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国外现货批发意向订单地址信息表
 * @TableName wholesale_intention_order_address
 */
@TableName(value ="wholesale_intention_order_address")
@Data
@EqualsAndHashCode(callSuper=false)
public class WholesaleIntentionOrderAddress extends SortEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 国外现货批发意向订单主键
     */
    private Long wholesaleIntentionOrderId;

    /**
     * 收件地址-国家
     */
    private String recipientCountry;
    /**
     * 收件地址-国家Code
     */
    private String recipientCountryCode;

    /**
     * 收件地址-州/省
     */
    private String recipientState;

    /**
     * 收件地址-州/省Code
     */
    private String recipientStateCode;

    /**
     * 收件地址-城市
     */
    private String recipientCity;

    /**
     * 收件地址-详细地址1
     */
    private String recipientAddress1;

    /**
     * 收件地址-详细地址2
     */
    private String recipientAddress2;

    /**
     * 收件地址-邮编
     */
    private String recipientZipCode;

    /**
     * 收件人姓名
     */
    private String recipientName;

    /**
     * 收件人电话
     */
    private String recipientPhone;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
