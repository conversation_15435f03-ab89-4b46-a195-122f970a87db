package com.zsmall.extend.wms.model.base;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 响应 - 翻页结果内容
 */
@Setter
@Getter
@Accessors(chain = true)
public class PageResult<T> {

    private Pageable pageable;

    private Sort sort;

    private Integer totalElements;

    private Integer totalPages;

    private Boolean last;

    private Boolean first;

    private Integer numberOfElements;

    private Integer size;

    private Integer number;

    private Integer empty;

    private List<T> content;

}
