package com.zsmall.order.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.order.entity.domain.OrderItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 子订单业务对象 order_item
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderItem.class, reverseConvertGenerate = false)
public class OrderItemBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 供货商租户编号
     */
    @NotBlank(message = "供货商租户编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String supplierTenantId;

    /**
     * 主订单表主键
     */
    @NotNull(message = "主订单表主键不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderId;

    /**
     * 子订单编号
     */
    @NotBlank(message = "子订单编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderItemNo;

    /**
     * 渠道类型
     */
    @NotBlank(message = "渠道类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String channelType;

    /**
     * 渠道店铺主键
     */
    @NotNull(message = "渠道店铺主键不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long channelId;

    /**
     * 第三方渠道子订单编号（如果有则记录）
     */
    @NotBlank(message = "第三方渠道子订单编号（如果有则记录）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String channelItemNo;

    /**
     * 第三方渠道履约ID（如果有则记录）
     */
    @NotBlank(message = "第三方渠道履约ID（如果有则记录）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String channelFulfillmentId;

    /**
     * 订单商品库存管理方
     */
    @NotBlank(message = "订单商品库存管理方不能为空", groups = {AddGroup.class, EditGroup.class})
    private String stockManager;

    /**
     * 物流类型：PickUp-自提，DropShipping-代发
     */
    @NotBlank(message = "物流类型：PickUp-自提，DropShipping-代发不能为空", groups = {AddGroup.class, EditGroup.class})
    private String logisticsType;

    /**
     * 使用的物流模板
     */
    @NotBlank(message = "使用的物流模板不能为空", groups = {AddGroup.class, EditGroup.class})
    private String logisticsTemplateNo;

    /**
     * 发货单状态（用于标记第三方仓库的发货单创建状态）
     */
    @NotBlank(message = "发货单状态（用于标记第三方仓库的发货单创建状态）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String shippingOrderState;

    /**
     * 子订单履约进度（未发货、已发货、已履约等）
     */
    @NotBlank(message = "子订单履约进度（未发货、已发货、已履约等）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String fulfillmentProgress;

    /**
     * 发货时间
     */
    @NotNull(message = "发货时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date dispatchedTime;

    /**
     * 履约时间
     */
    @NotNull(message = "履约时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date fulfillmentTime;

    /**
     * 订单状态
     */
    @NotBlank(message = "订单状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderState;

    /**
     * Sku唯一编号（ItemNo.）
     */
    @NotBlank(message = "Sku唯一编号（ItemNo.）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String productSkuCode;

    /**
     * 参与活动类型（为空代表未参与活动）
     */
    @NotBlank(message = "参与活动类型（为空代表未参与活动）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String activityType;

    /**
     * 参与活动编号（为空代表未参与）
     */
    @NotBlank(message = "参与活动编号（为空代表未参与）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String activityCode;

    /**
     * 子订单商品总数量
     */
    @NotNull(message = "子订单商品总数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer totalQuantity;

    /**
     * 已归还的商品数量（退款等操作，需要归还库存）
     */
    @NotNull(message = "已归还的商品数量（退款等操作，需要归还库存）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer restockQuantity;

    /**
     * 供货商应得收入
     */
    @NotNull(message = "供货商应得收入不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal supplierIncomeEarned;

    /**
     * 原始应付单价（供货商）
     */
    @NotNull(message = "原始应付单价（供货商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal originalPayableUnitPrice;

    /**
     * 原始应付总金额（供货商）
     */
    @NotNull(message = "原始应付总金额（供货商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal originalPayableTotalAmount;

    /**
     * 原始实际支付单价（供货商）
     */
    @NotNull(message = "原始实际支付单价（供货商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal originalActualUnitPrice;

    /**
     * 原始实际支付总金额（供应商）
     */
    @NotNull(message = "原始实际支付总金额（供应商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal originalActualTotalAmount;

    /**
     * 原始售后可执行金额（供货商）
     */
    @NotNull(message = "原始售后可执行金额（供货商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal originalRefundExecutableAmount;

    /**
     * 平台应付单价（平台、分销商）
     */
    @NotNull(message = "平台应付单价（平台、分销商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal platformPayableUnitPrice;

    /**
     * 平台应付总金额（平台、分销商）
     */
    @NotNull(message = "平台应付总金额（平台、分销商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal platformPayableTotalAmount;

    /**
     * 平台实际支付单价（平台、分销商）
     */
    @NotNull(message = "平台实际支付单价（平台、分销商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal platformActualUnitPrice;

    /**
     * 平台实际支付总金额（平台、分销商）
     */
    @NotNull(message = "平台实际支付总金额（平台、分销商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal platformActualTotalAmount;

    /**
     * 平台售后可执行金额（平台、分销商）
     */
    @NotNull(message = "平台售后可执行金额（平台、分销商）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal platformRefundExecutableAmount;

    /**
     * 渠道销售单价（分销商铺货时Mark Up之后的价格）
     */
    @NotNull(message = "渠道销售单价（分销商铺货时Mark Up之后的价格）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal channelSaleUnitPrice;

    /**
     * 渠道销售总金额（分销商铺货时Mark Up之后的价格乘以数量）
     */
    @NotNull(message = "渠道销售总金额（分销商铺货时Mark Up之后的价格乘以数量）不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal channelSaleTotalAmount;


}
