package com.zsmall.extend.shop.config.properties;

import com.zsmall.extend.shop.amazon.bean.FileTmpBean;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * amazon 配置
 */
@Data
@ConfigurationProperties(prefix = "thirdparty-shop.amazon")
public class AmazonProperties {
    /**
     * 过滤开关
     */
    private Boolean enabled;
    /**
     * 应用重定向地址
     */
    private String appRedirectUrl;
    /**
     * 业务网站登录地址
     */
    private String websiteLoginUrl;
    /**
     * 应用授权跳转地址
     */
    private String authorizeRedirectUrl;

    /**
     * 文件相关操作缓存目录
     */
    private FileTmpBean fileTmp;
}
