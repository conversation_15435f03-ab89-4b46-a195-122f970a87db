package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import com.zsmall.common.enums.order.LogisticsProgress;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 子订单物流跟踪单对象 order_item_tracking_record
 *
 * <AUTHOR>
 * @date 2023-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_item_tracking_record")
public class OrderItemTrackingRecord extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 子订单编号
     */
    private String orderItemNo;

    /**
     * 关联出货单编号
     */
    private String shippingNo;

    /**
     * 最小库存单位
     */
    private String sku;

    /**
     * ItemNo
     */
    private String productSkuCode;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 发货时间
     */
    private Date dispatchedTime;

    /**
     * 履约时间
     */
    private Date fulfillmentTime;

    /**
     * 物流承运商
     */
    private String logisticsCarrier;

    /**
     * 物流服务
     */
    private String logisticsService;

    /**
     * 物流跟踪单号
     */
    private String logisticsTrackingNo;

    /**
     * 物流进度（物流状态异常、未发货、标签创建、已发货、派送中、已履约等）
     */
    private LogisticsProgress logisticsProgress;

    /**
     * 出货仓库编号
     */
    private String warehouseCode;

    /**
     * 出货仓库唯一系统编号
     */
    private String warehouseSystemCode;

    /**
     * 跟踪单是否由系统托管轮询（0-否，1-是）
     */
    private Boolean systemManaged;

    /**
     * 调用第三方物流接口次数
     */
    private Integer callingApi = 0;

    /**
     * 初次查询物流失败后，二次确认日期
     */
    private String confirmDate;

    /**
     * 上次查询物流信息时间
     */
    private Date lastQueryTime;

    /**
     * 第三方平台查询结果代号
     */
    private String thirdPartyCode;

    /**
     * 第三方平台查询结果信息
     */
    private String thirdPartyMessage;

    /**
     * 第三方平台物流信息更新时间
     */
    private String thirdPartyDateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
