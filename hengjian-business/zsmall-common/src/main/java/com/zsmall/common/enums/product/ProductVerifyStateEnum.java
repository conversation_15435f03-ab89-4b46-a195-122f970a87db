package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;

import java.util.Locale;

/**
 * 商品审核状态枚举
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
@AllArgsConstructor
public enum ProductVerifyStateEnum implements IEnum<String> {

    /**
     * 未提交草稿
     */
    Draft("草稿", "Draft"),

    /**
     * 审核中
     */
    Pending("审核中", "Pending"),

    /**
     * 已通过
     */
    Accepted("已通过", "Accepted"),

    /**
     * 已驳回
     */
    Rejected("已驳回", "Rejected"),

    /**
     * 废弃，当新的审核申请提交时，该商品所有旧的Pending记录都会变成此状态
     */
    Abandoned("废弃", "Abandoned"),

    ;

    private String zh_CN;

    private String en_US;

    public String getByLocale(String language) {
        if (Locale.SIMPLIFIED_CHINESE.toString().equals(language)) {
            return zh_CN;
        } else {
            return en_US;
        }
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
