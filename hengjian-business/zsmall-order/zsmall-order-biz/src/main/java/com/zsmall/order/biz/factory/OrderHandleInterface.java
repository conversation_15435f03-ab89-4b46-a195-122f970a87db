package com.zsmall.order.biz.factory;

import com.zsmall.common.enums.order.OrderType;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.vo.order.OrderDetailVo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVo;

/**
 * 订单处理-Interface
 *
 * <AUTHOR>
 * @date 2023/2/20
 */
public interface OrderHandleInterface {

    /**
     * 构建订单Body
     *
     * @param orders
     * @param activityType
     * @return
     */
    OrderPageVo buildOrderBody(Orders orders, String activityType);

    /**
     * 构建订单详情
     *
     * @param orders
     * @return
     */
    OrderDetailVo buildOrderDetail(Orders orders);

    /**
     * 验证订单类型
     *
     * @param orderType
     * @return
     */
    boolean isThisImpl(OrderType orderType);

    OrderDetailVo buildOrderDetailForExport(Orders item);
}
