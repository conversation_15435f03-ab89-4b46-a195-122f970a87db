package com.zsmall.order.biz.factory;

import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderLogisticsInfo;
import com.zsmall.order.entity.domain.Orders;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;

/**
 * 渠道订单检查工厂类接口
 * <AUTHOR>
 * @date 2023/6/25
 */
public interface ChannelOrderInspectionService extends InitializingBean {

    /**
     * 物流检查
     * @param order
     * @param orderItemList
     * @param orderLogisticsInfo
     */
    void logisticsInspection(Orders order, List<OrderItem> orderItemList, OrderLogisticsInfo orderLogisticsInfo) throws RStatusCodeException;

}
