package com.zsmall.product.entity.domain.bo.label;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-查询用户的反馈信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "请求参数-标签参数")
public class ReqLabelFormBody {

    @Schema(title = "标签id")
    private Long id;

    @Schema(title = "标签名称")
    private String labelName;

    @Schema(title = "排序序号")
    private Integer sort;

    @Schema(title = "排序规则")
    private String sortRule;
}
