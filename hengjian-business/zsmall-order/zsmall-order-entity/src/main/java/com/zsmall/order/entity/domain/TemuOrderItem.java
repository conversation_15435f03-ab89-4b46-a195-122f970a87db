package com.zsmall.order.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/25 16:42
 */
@Data
@TableName("temu_order_item")
public class TemuOrderItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private String sku;
    private String asin;

    private Integer quantity;
    private BigDecimal subtotal;
    private String currencyCode;
    private String channelOrderItemId;

    private BigDecimal price;
    private BigDecimal salesTotalAmount;
    private String skuName;
    private String orderNum;
}
