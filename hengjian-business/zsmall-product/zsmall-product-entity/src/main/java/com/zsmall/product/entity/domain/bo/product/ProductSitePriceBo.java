package com.zsmall.product.entity.domain.bo.product;

import com.zsmall.product.entity.domain.vo.member.RuleLevelSkuPriceVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/12/19 11:14
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "产品-站点价格")
public class ProductSitePriceBo {

    /**
     * id,如果是第一次添加，id为空，如果是修改，id不为空-实际是productSkuPrice表的主键
     * 会员价业务无上述业务
     */
    private Long id;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 货币符号
     */
    private String currencySymbol;
    private BigDecimal msrp;


    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 国家代码
     */
    private String countryCode;

    @Setter
    private List<RuleLevelSkuPriceVo> skuPriceVoList;

    private BigDecimal dropShippingPrice;

    private BigDecimal pickUpPrice;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;
    /**
     * 操作费
     */
    @NotNull(message = "操作费不能为空")
    private BigDecimal operationFee;
    /**
     * 尾程派送费
     */
    @NotNull(message = "尾程派送费不能为空")
    private BigDecimal finalDeliveryFee;
}
