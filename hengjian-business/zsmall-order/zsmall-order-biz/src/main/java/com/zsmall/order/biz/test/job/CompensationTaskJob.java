package com.zsmall.order.biz.test.job;

import com.alibaba.fastjson.JSON;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.order.biz.service.CompensationJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/10/16 18:08
 */
@Slf4j
@Service
public class CompensationTaskJob {
    @Resource
    private CompensationJobService compensationJobService;

    @XxlJob("abnormalOrderCompensationHandler")
    public void abnormalOrderCompensationHandler() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobSearchVO xxlBody = JSON.parseObject(jobParam, XxlJobSearchVO.class);
        Integer otherDay = xxlBody.getOtherDay();
        TenantHelper.ignore(()->compensationJobService.abnormalOrderCompensationHandler(otherDay));

    }
}
