package com.hengjian.common.translation.core.impl;

import com.hengjian.common.core.service.DeptService;
import com.hengjian.common.translation.annotation.TranslationType;
import com.hengjian.common.translation.constant.TransConstant;
import com.hengjian.common.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;

/**
 * 部门翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.DEPT_ID_TO_NAME)
public class DeptNameTranslationImpl implements TranslationInterface<String> {

    private final DeptService deptService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof String) {
            return deptService.selectDeptNameByIds(key.toString());
        } else if (key instanceof Long) {
            return deptService.selectDeptNameByIds(key.toString());
        }
        return null;
    }
}
