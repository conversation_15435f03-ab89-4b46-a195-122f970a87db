package com.zsmall.common.enums.warehouse;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.zsmall.common.enums.product.StockManagerEnum;

/**
 * 仓库类型枚举
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
public enum WarehouseTypeEnum implements IEnum<String> {

    /**
     * 自有仓库
     */
    OwnWarehouse,

    /**
     * 恒健仓库
     */
    BizArk,

    ;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }

    public StockManagerEnum toStockManager() {
        return StockManagerEnum.valueOf(this.name());
    }
}
