package com.zsmall.system.entity.domain.bo.settleInBasic;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.TenantSupSettleInReviewRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 供应商入驻信息审核记录业务对象 tenant_sup_settle_in_review_record
 *
 * <AUTHOR>
 * @date 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantSupSettleInReviewRecord.class, reverseConvertGenerate = false)
public class TenantSupSettleInReviewRecordBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = {AddGroup.class, EditGroup.class})
    private String creatorCode;

    /**
     * 审核人ID
     */
    @NotBlank(message = "审核人ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String reviewUserCode;

    /**
     * 供应商入驻基础信息id
     */
    @NotNull(message = "供应商入驻基础信息id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long basicId;

    /**
     * 审核原因
     */
    @NotBlank(message = "审核原因不能为空", groups = {AddGroup.class, EditGroup.class})
    private String reviewReason;

    /**
     * 审核时间
     */
    @NotNull(message = "审核时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date reviewDateTime;

    /**
     * 审核状态（1-审核中，2-通过，3-驳回）
     */
    @NotNull(message = "审核状态（1-审核中，2-通过，3-驳回）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long reviewStatus;


}
