package com.zsmall.order.biz.test.factory;

import com.zsmall.common.handler.AbstractOrderHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 14:09
 */
@Component
public class ThirdOrderApiV2Factory {
    @Resource
    private Map<String, AbstractOrderHandler> strategyMap;

    public AbstractOrderHandler getInvokeStrategy(String strategyName){
        return strategyMap.get(strategyName);
    }
}
