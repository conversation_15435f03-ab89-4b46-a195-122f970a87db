package com.zsmall.system.entity.domain.bo.transaction;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.system.entity.domain.TransactionRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易记录业务对象 transaction_record
 *
 * <AUTHOR>
 * @date 2023-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TransactionRecord.class, reverseConvertGenerate = false)
public class TransactionRecordBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 交易记录编号
     */
    @NotBlank(message = "交易记录编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionNo;

    /**
     * 交易主类型（本平台与外部平台资金流动：Recharge-充值，Withdrawal-提现；本平台内部资金流动：Income-收入，Expenditure-支出）
     */
    @NotBlank(message = "交易主类型（本平台与外部平台资金流动：Recharge-充值，Withdrawal-提现；本平台内部资金流动：Income-收入，Expenditure-支出）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionType;

    /**
     * 交易子类型（记录交易具体的业务类型，如主类型为支出，子类型可以为订单支付、仓储费支付、订金支付等）
     */
    @NotBlank(message = "交易子类型（记录交易具体的业务类型，如主类型为支出，子类型可以为订单支付、仓储费支付、订金支付等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionSubType;

    /**
     * 交易前余额
     */
    @NotNull(message = "交易前余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal beforeBalance;

    /**
     * 交易金额
     */
    @NotNull(message = "交易金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal transactionAmount;

    /**
     * 交易后余额
     */
    @NotNull(message = "交易后余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal afterBalance;

    /**
     * 交易时间
     */
//    @NotNull(message = "交易时间不能为空", groups = { EditGroup.class })
    private Date transactionTime;

    /**
     * 实际交易时间
     */
//    @NotNull(message = "实际交易时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionTimeString;

    /**
     * 交易备注
     */
    @NotBlank(message = "交易备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionNote;

    /**
     * 交易状态
     */
    @NotBlank(message = "交易状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionState;


}
