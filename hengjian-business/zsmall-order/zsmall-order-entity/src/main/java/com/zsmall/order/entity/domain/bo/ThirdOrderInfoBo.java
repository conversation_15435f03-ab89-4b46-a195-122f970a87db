package com.zsmall.order.entity.domain.bo;

import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.zsmall.order.entity.domain.ThirdOrderInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 第三方订单信息主业务对象 third_order_info
 *
 * <AUTHOR> Li
 * @date 2024-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ThirdOrderInfo.class, reverseConvertGenerate = false)
public class ThirdOrderInfoBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺名称
     */
    private String accountId;

    /**
     * 第三方订单号
     */
    private String orderNum;

    /**
     * 订单总金额
     */
    private BigDecimal amount;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 发货状态
     */
    private String shippingStatus;

    /**
     * 买家名称
     */
    private String buyName;

    /**
     * 买家email
     */
    private String buyEmail;

    /**
     * display_seller
     */
    private String displaySeller;

    /**
     * 订单发货类型  MFN  AFM
     */
    private String fulfillmentChannel;

    /**
     * 发货等级
     */
    private String shipServiceLevel;

    /**
     * 收件人
     */
    private String shippingName;

    /**
     * 地址1
     */
    private String addressline1;

    /**
     * 地址2
     */
    private String addressline2;

    /**
     * 城市
     */
    private String city;

    /**
     * 省/州代码
     */
    private String stateOrRegion;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家code
     */
    private String countryCode;

    /**
     * 邮编
     */
    private String postalCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 创建时间
     */
    private String created;

    /**
     * 付款时间
     */
    private String paymentDate;

    /**
     * 更新时间
     */
    private String updated;

    /**
     * 最晚发货时间
     */
    private String latestShipDate;

    /**
     * 最早发货时间
     */
    private String earliestShipDate;

    /**
     * 是否商业订单
     */
    private String isBusinessOrder;

    /**
     * 是否为prime的订单
     */
    private String isPrime;


}
