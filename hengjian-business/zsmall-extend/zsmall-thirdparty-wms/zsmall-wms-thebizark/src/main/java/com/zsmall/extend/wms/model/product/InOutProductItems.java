package com.zsmall.extend.wms.model.product;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 响应输出 - 渠道商品
 */
@Getter
@Setter
@Accessors(chain = true)
public class InOutProductItems {

    /**
     * sku
     */
    private String sku;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 入库产品数量
     */
    private Integer qty;
    /**
     * 销售金额 , 当前sku总金额
     */
    private Double skuAmount;

    /**
     * 投保金额
     */
    private Double insureAmount;

    /**
     * 是否保价 0否1是
     */
    private Integer isInsure;

    /**
     * 是否签收 0否 1是
     */
    private Integer isSignature;

    /**
     * 采购成本，影响库存成本等数据
     */
    private Double cost;

}
