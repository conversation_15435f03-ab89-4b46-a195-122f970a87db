package com.zsmall.common.enums.tiktok;

import com.hengjian.common.log.enums.BusinessType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/3 14:26
 */
@Getter
@AllArgsConstructor
public enum TikTokBusinessHandlerEnums {

    tiktok_product("tiktokProductHandler", BusinessType.PRODUCT.name()),
    tiktok_product_v2("tiktokProductV2Handler", BusinessType.PRODUCT_V2.name()),
    tiktok_order_v2("tiktokBusinessHandlerV2",BusinessType.ORDER_V2.name());



    private final String handler;
    private final String name;

    /**
     * 功能描述：按标记获取处理程序
     *
     * @param name 名字
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/02/03
     */
    public static String getHandlerByTag(String name){
        TikTokBusinessHandlerEnums[] values = TikTokBusinessHandlerEnums.values();
        for (TikTokBusinessHandlerEnums value : values) {
            if(value.name.equals(name)){
                return value.getHandler();
            }
        }
        return null;
    }
}
