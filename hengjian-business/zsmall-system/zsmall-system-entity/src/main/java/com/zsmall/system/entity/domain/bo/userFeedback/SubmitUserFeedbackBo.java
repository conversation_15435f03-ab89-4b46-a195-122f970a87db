package com.zsmall.system.entity.domain.bo.userFeedback;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 请求体-提交用户反馈
 *
 * <AUTHOR>
 * @date 2023/9/11
 */
@Data
public class SubmitUserFeedbackBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邮箱
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String email;

    /**
     * 反馈标题
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String feedbackTitle;

    /**
     * 反馈类型
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String feedbackType;

    /**
     * 反馈内容
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String feedbackContent;

    /**
     * 附件集合
     */
    private List<UserFeedbackAttachmentBo> attachmentList;

}
