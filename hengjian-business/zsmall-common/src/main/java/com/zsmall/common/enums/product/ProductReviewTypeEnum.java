package com.zsmall.common.enums.product;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 商品审核类型
 *
 * <AUTHOR>
 */
public enum ProductReviewTypeEnum implements IEnum<String> {

    /**
     * 新商品
     */
    NewProduct,

    /**
     * 新商品Sku
     */
    NewProductSku,

    /**
     * 价格相关 -原逻辑是Price代表着价格修改，现在拆分为Price和PriceDelate
     */
    Price,

    ;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }
}
