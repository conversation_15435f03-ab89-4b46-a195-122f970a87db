package com.zsmall.system.entity.domain.bo.salesChannel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TenantSalesChannelExportDTO {
    @ExcelProperty(value  = "渠道类型")
    private String channelType;

    @ExcelProperty(value  = "渠道名称")
    private String channelName;

    @ExcelProperty(value  = "渠道店铺别名")
    private String channelAlias;

    @ExcelProperty(value  = "发货方式")
    private String logisticsType;

    @ExcelProperty(value  = "状态")
    private String state;

    /**
     * 店铺仓库名称
     */
    @ExcelProperty(value   = "店铺仓库名称")
    private String channelWarehouseName;

    /**
     * 店铺仓库标识符
     */
    @ExcelProperty(value   = "店铺仓库标识符")
    private String channelWarehouseCode;
    /**
     * 渠道名称
     */
    @ExcelProperty(value   = "恒健仓库")
    private String warehouseCode;

}
