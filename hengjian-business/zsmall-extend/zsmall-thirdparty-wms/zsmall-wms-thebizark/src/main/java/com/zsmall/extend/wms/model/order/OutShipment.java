package com.zsmall.extend.wms.model.order;

import cn.hutool.core.date.DateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 发货信息
 */
@Setter
@Getter
@Accessors(chain = true)
public class OutShipment {

    /**
     * Tracking号
     */
    private String trackingNo;

    /**
     * 发货方式
     */
    private String carrier;

    /**
     * 发货数量
     */
    private Integer shipedQuantity;

    /**
     * 发货时间
     */
    private DateTime shipmentDate;

    /**
     * 发货产品
     */
    private String erpSku;

    /**
     * 成本
     */
    private Double cost;

    /**
     * 成本
     */
    private Integer packageFlag;

}
