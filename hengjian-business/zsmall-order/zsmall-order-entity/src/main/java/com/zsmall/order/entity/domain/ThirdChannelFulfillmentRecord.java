package com.zsmall.order.entity.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.FulfillmentPushStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 第三方渠道履约记录对象 third_channel_fulfillment_record
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("third_channel_fulfillment_record")
public class ThirdChannelFulfillmentRecord extends NoDeptBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 渠道店铺主键
     */
    private Long channelId;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 主订单编号
     */
    private String orderNo;

    /**
     * 子订单编号
     */
    private String orderItemNo;

    /**
     * 第三方渠道子订单编号（如果有则记录）
     */
    private String channelItemNo;

    /**
     * 销售渠道订单编号（如果有则记录）
     */
    private String channelOrderNo;

    /**
     * 销售渠道订单号（如果有则记录，展示用）
     */
    private String channelOrderName;

    /**
     * 第三方渠道履约ID（如果有则记录）
     */
    private String channelFulfillmentId;

    /**
     * 履约推送状态（0-未推送，1-等待推送，2-已推送，3-推送失败）
     */
    private FulfillmentPushStateEnum fulfillmentPushState = FulfillmentPushStateEnum.NotPushed;

    /**
     * 第三方渠道履约失败信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONObject channelFulfillmentMessage;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
