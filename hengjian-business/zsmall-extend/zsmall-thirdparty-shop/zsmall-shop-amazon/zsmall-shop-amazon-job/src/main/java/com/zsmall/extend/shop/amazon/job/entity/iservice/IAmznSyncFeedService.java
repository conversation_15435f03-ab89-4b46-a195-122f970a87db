package com.zsmall.extend.shop.amazon.job.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.extend.shop.amazon.job.constants.AmznFeedCreateStatus;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncFeed;
import com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncFeedMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【job_amzn_order_sync(任务-亚马逊订单同步)】的数据库操作Service实现
 * @createDate 2023-11-01 12:09:42
 */
@Service
public class IAmznSyncFeedService extends ServiceImpl<AmznSyncFeedMapper, AmznSyncFeed> {

    // 结束执行的状态集
    private static final String[] END_STATUS = {AmznFeedCreateStatus.Error.name(),
        AmznFeedCreateStatus.PartialSuccess.name(), AmznFeedCreateStatus.Success.name(), AmznFeedCreateStatus.Processing.name()};

    /**
     * 查询等待中的任务
     *
     * @return
     */
    public List<AmznSyncFeed> selectWaitingTask() {
        LambdaQueryWrapper<AmznSyncFeed> lqw = new LambdaQueryWrapper<>();
        lqw.notIn(AmznSyncFeed::getFeedStatus, END_STATUS);

        return baseMapper.selectList(lqw);
    }
}




