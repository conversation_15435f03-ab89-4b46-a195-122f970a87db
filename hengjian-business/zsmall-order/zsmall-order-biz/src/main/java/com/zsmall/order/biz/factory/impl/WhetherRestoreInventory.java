package com.zsmall.order.biz.factory.impl;

import cn.hutool.core.util.NumberUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.constant.OrderRefundRuleConstant;
import com.zsmall.order.factory.RefundRuleFactory;
import com.zsmall.order.factory.RefundRuleService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.OrderRefundItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 是否还原库存
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@Service("whetherRestoreInventory")
public class WhetherRestoreInventory implements RefundRuleService {

    @Override
    public void afterPropertiesSet() throws Exception {
        RefundRuleFactory.register("WhetherRestoreInventory", this);
    }

    /**
     * 售后规则“是否”类条件处理
     *
     * @param whetherValue
     * @param bo
     * @param orderItem
     * @param orderRefund
     * @param orderRefundItem
     */
    @Override
    public void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo bo, OrderItem orderItem,
                                        OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException {

        if (OrderRefundRuleConstant.WhetherRestoreInventory.NO_RESTORE.equals(whetherValue)) {
            orderRefund.setRefundQuantity(0);
            orderRefundItem.setRefundQuantity(0);
        } else if (OrderRefundRuleConstant.WhetherRestoreInventory.FULL_ORDER_RESTORE.equals(whetherValue)) {
            Integer totalQuantity = orderItem.getTotalQuantity();
            orderRefund.setRefundQuantity(totalQuantity);
            orderRefundItem.setRefundQuantity(totalQuantity);
        } else if (OrderRefundRuleConstant.WhetherRestoreInventory.EXECUTABLE_AMOUNT_ZERO_RESTORE.equals(whetherValue)) {
            if (NumberUtil.equals(orderItem.getPlatformRefundExecutableAmount(), BigDecimal.ZERO)) {
                Integer totalQuantity = orderItem.getTotalQuantity();
                orderRefund.setRefundQuantity(totalQuantity);
                orderRefundItem.setRefundQuantity(totalQuantity);
            } else {
                orderRefund.setRefundQuantity(0);
                orderRefundItem.setRefundQuantity(0);
            }
        }

    }

    /**
     * 售后规则“是否”类条件处理（主订单适用）
     *
     * @param whetherValue
     * @param bo
     * @param order
     * @param orderItem
     * @param orderRefund
     * @param orderRefundItem
     */
    @Override
    public void refundRuleWhetherHandle(Integer whetherValue, SubmitRefundApplyBo bo, Orders order,
                                        OrderItem orderItem, OrderRefund orderRefund, OrderRefundItem orderRefundItem) throws RStatusCodeException {

        if (OrderRefundRuleConstant.WhetherRestoreInventory.NO_RESTORE.equals(whetherValue)) {
            orderRefund.setRefundQuantity(0);
            orderRefundItem.setRefundQuantity(0);
        } else if (OrderRefundRuleConstant.WhetherRestoreInventory.FULL_ORDER_RESTORE.equals(whetherValue)) {
            Integer totalNum = orderRefund.getRefundQuantity();
            Integer num = orderItem.getTotalQuantity();
            orderRefund.setRefundQuantity(NumberUtil.add(totalNum, num).intValue());
            orderRefundItem.setRefundQuantity(num);
        } else if (OrderRefundRuleConstant.WhetherRestoreInventory.EXECUTABLE_AMOUNT_ZERO_RESTORE.equals(whetherValue)) {
            if (NumberUtil.equals(order.getPlatformRefundExecutableAmount(), BigDecimal.ZERO)) {
                Integer totalNum = orderRefund.getRefundQuantity();
                Integer num = orderItem.getTotalQuantity();
                orderRefund.setRefundQuantity(NumberUtil.add(totalNum, num).intValue());
                orderRefundItem.setRefundQuantity(num);
            } else {
                orderRefund.setRefundQuantity(0);
                orderRefundItem.setRefundQuantity(0);
            }
        }

    }

}
