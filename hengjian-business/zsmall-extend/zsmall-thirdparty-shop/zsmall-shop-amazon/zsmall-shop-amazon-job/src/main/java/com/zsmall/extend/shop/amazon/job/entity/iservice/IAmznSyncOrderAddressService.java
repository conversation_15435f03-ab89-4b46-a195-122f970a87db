package com.zsmall.extend.shop.amazon.job.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderAddress;
import com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncOrderAddressMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【amzn_sync_order_address】的数据库操作Service实现
* @createDate 2023-11-01 10:18:56
*/
@Service
public class IAmznSyncOrderAddressService extends ServiceImpl<AmznSyncOrderAddressMapper, AmznSyncOrderAddress> {

    public List<AmznSyncOrderAddress> queryByAmazonOrderId(String amazonOrderId) {
        return lambdaQuery().eq(AmznSyncOrderAddress::getAmazonOrderId, amazonOrderId).list();
    }

}




