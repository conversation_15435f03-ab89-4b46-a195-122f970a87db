package com.zsmall.extend.shop.rakuten.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrJoiner;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Rakuten-通用响应体
 *
 * <AUTHOR>
 * @date 2023/10/8
 */
@Data
public class RakutenResult implements Serializable {

    private static final long serialVersionUID = 1L;

    private Result result;

    /**
     * 错误列表
     */
    private List<Error> errors;

    @Data
    public class Error {

        private String code;

        private String message;

        private Metadata metadata;

    }

    @Data
    public class Metadata {

        private String propertyPath;

    }

    @Data
    public class Result {

        // 结果状态
        private Status status;

        // 文件插入结果
        private CabinetFileInsertResult cabinetFileInsertResult;

    }

    // 状态类
    @Data
    public class Status {

        private String interfaceId;

        private String systemStatus;

        private String message;

        private String requestId;

        private List<Object> requests;

    }

    // 文件插入结果类
    @Data
    public class CabinetFileInsertResult {

        private Integer resultCode;

        private Long fileId;

    }

    public String toString() {
        if (CollUtil.isNotEmpty(this.getErrors())) {
            List<RakutenResult.Error> errors = this.getErrors();
            if (CollUtil.isNotEmpty(errors)) {
                StrJoiner errorJoiner = new StrJoiner(";");
                for (int i = 0; i < errors.size(); i++) {
                    String idx = String.valueOf(i + 1);
                    Error error = errors.get(i);
                    errorJoiner.append(idx + ". " + error.getMessage());
                }
                return errorJoiner.toString();
            }
        }
        return "";
    }

}
