package com.zsmall.product.entity.domain.bo.product;

import cn.hutool.json.JSONArray;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.product.ProductTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductAttribute;
import com.zsmall.product.entity.domain.ProductCategoryRelation;
import com.zsmall.product.entity.domain.ProductGlobalAttribute;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuImportBo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 商品SPU导入视图对象
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Product.class, reverseConvertGenerate = false)
public class ProductImportBo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 存在
     */
    private Boolean isExists;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 产品描述
     */
    private String description;

    /**
     * SPU唯一编号
     */
    private String productCode;

    /**
     * 商品类型
     */
    private ProductTypeEnum productType;

    /**
     * 货架状态：OnShelf-上架；OffShelf-下架；ForcedOffShelf-强制下架
     */
    private ShelfStateEnum shelfState;

    /**
     * 初次上架时间
     */
    private Date firstOnShelfTime;

    /**
     * 最新上架时间
     */
    private Date lastOnShelfTime;

    /**
     * 审核状态
     */
    private ProductVerifyStateEnum verifyState;

    /**
     * 审核时间（商家商品字段。商家修改商品后员工审核操作的时间）
     */
    private Date verifyTime;

    /**
     * 支持的物流：All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发
     */
    private SupportedLogisticsEnum supportedLogistics;

    /**
     * 禁售渠道（多选，&号分隔）
     */
    private JSONArray forbiddenChannel;

    /**
     * 归属分类id（一般情况下为商品所属分类树的最底层）
     */
    private Long belongCategoryId;

    /**
     * 产品被下载次数
     */
    private Integer downloadCount;

    /**
     * 铺货渠道主键
     */
    private Long channelId;

    /**
     * 铺货渠道类型
     */
    private ChannelTypeEnum channelType;

    /**
     * 商品分类关系（携带用）
     */
    public List<ProductCategoryRelation> categoryRelationList;

    /**
     * 商品SPU属性（携带用，通用规格和商品特色用）
     */
    private List<ProductAttribute> productAttributeList = new ArrayList<>();

    /**
     * 商品SPU属性（携带用，可选规格专用）
     */
    private List<ProductAttribute> optionalSpecList = new ArrayList<>();

    /**
     * 商品使用的全局可选规格（携带用）
     */
    private List<ProductGlobalAttribute> globalAttributeList = new ArrayList<>();



    /**
     * 商品SKU数组（携带用）
     */
    private List<ProductSkuImportBo> productSkuVoList = new ArrayList<>();
    /**
     * 已存在的规格值组合
     */
    private List<String> specValNameList = new ArrayList<>();

    public void addProductSku(ProductSkuImportBo productSkuVo) {
        productSkuVoList.add(productSkuVo);
    }

    public void addSpecValName(String specValName) { specValNameList.add(specValName); }

    public Boolean containsSpecValName(String specValName) { return specValNameList.contains(specValName); }

}
